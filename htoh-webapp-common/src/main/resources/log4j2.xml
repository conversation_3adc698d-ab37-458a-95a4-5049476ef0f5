<?xml version="1.0" encoding="UTF-8"?> 
<!-- status=debug 可以查看log4j的装配过程 -->  
<configuration status="off" monitorInterval="1800">  
    <properties>  
        <property name="LOG_HOME">${sys:catalina.home}/logs</property>  
        <!-- 日志备份目录 -->  
        <property name="BACKUP_HOME">${sys:catalina.home}/logs/backup</property>  
        <property name="SERVER_NAME">htohwebapp</property>  
    </properties>  
    <appenders>  
        <!-- 定义控制台输出 -->  
        <Console name="Console" target="SYSTEM_OUT" follow="true">  
            <PatternLayout charset="GBK"  pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n" />  
        </Console>  
        <!-- 程序员调试日志 -->  
        <RollingRandomAccessFile name="DevLog" fileName="${LOG_HOME}/${SERVER_NAME}.log"  
            filePattern="${LOG_HOME}/${SERVER_NAME}.%d{yyyy-MM-dd-HH}.log">  
            <PatternLayout charset="GBK" pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n" />  
            <Policies>  
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <SizeBasedTriggeringPolicy size="10 MB" />  
            </Policies> 
            <DefaultRolloverStrategy max="20" /> 
        </RollingRandomAccessFile>  
    </appenders>  
    <loggers>  
        <!-- 3rdparty Loggers -->  
        <logger name="org.springframework.core" level="info"></logger>  
        <logger name="org.springframework.beans" level="info"></logger>  
        <logger name="org.springframework.context" level="info"></logger>  
        <logger name="org.springframework.web" level="info"></logger>  
        <!-- Root Logger -->  
        <root level="DEBUG">  
            <appender-ref ref="DevLog" />  
            <appender-ref ref="Console" />  
        </root>  
    </loggers>  
</configuration>