DROP FUNCTION
IF EXISTS f_getChildrenChannelId;

CREATE FUNCTION `f_getChildrenChannelId` (p_channelId INT, p_type INT) RETURNS VARCHAR (4000)
BEGIN
DECLARE
		sTemp VARCHAR (4000);

DECLARE
	sTempChd VARCHAR (4000);

DECLARE
	sTempType INT;


SET sTemp = '$';


SET sTempChd = cast(p_channelId AS CHAR);


SET sTempType = p_type;


WHILE sTempChd IS NOT NULL DO

SET sTemp = CONCAT(sTemp, ',', sTempChd);

SELECT
	group_concat(id) INTO sTempChd
FROM
	t_channel
WHERE
	FIND_IN_SET(pid, sTempChd) > 0
AND type = sTempType
AND STATUS = 0
AND is_index = 1;


END
WHILE;

RETURN substring(sTemp, 3);


END;