<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


<!--    &lt;!&ndash; quartz定时配置 start &ndash;&gt;-->
<!--    &lt;!&ndash; 工作的bean &ndash;&gt;-->
<!--    <bean id="datasync" class="com.htohcloud.aqg.quartzsync.AggDataSyncQuartz" />-->
<!--    &lt;!&ndash; 科强的bean &ndash;&gt;-->
<!--    <bean id="kqdatasync" class="com.htohcloud.handring.controller.KgDataSyncQuartz" />-->
<!--    &lt;!&ndash; 工作的bean &ndash;&gt;-->
<!--    <bean id="jldatasync" class="com.htohcloud.controller.quartz.JlDataSyncQuartz" />-->
<!--    &lt;!&ndash; 睡眠卫士数据同步10秒同步&ndash;&gt;-->
<!--    <bean id="smwsdatasync" class="com.htohcloud.smws.quartz.SmwsDataSyncQuartz" />-->
<!--    &lt;!&ndash; 生日提醒 30分钟&ndash;&gt;-->
<!--    <bean id="nursebirthdayhint" class="com.htohcloud.oldie.controller.app.quartz.NurseBirthdayHintQuartz" />-->
<!--    &lt;!&ndash; 基础定时线程服务，每1个小时启动一次同步 &ndash;&gt;-->
<!--    <bean id="basesync" class="com.ming.cms.controller.quartz.BaseTimerTaskQuartz" />-->
<!--    &lt;!&ndash;  24分钟-->
<!--    <bean id="undetectedwarn" class="com.htohcloud.weixin.controller.quartz.UndetectedWarnQuartz" />&ndash;&gt;-->
<!--    &lt;!&ndash; 一体机数据同步至总表线程服务，每1个小时启动一次同步 5 * 60 * 1000&ndash;&gt;-->
<!--    <bean id="alldatasync" class="com.htohcloud.cm.controller.quartz.AllDataSyncQuartz" />-->
<!--    &lt;!&ndash; 一体机数据同步线程服务，每1个小时启动一次同步  5 * 60 * 1000&ndash;&gt;-->
<!--    <bean id="ytjdatasync" class="com.htohcloud.controller.quartz.YtjDataSyncQuartz" />-->
<!--    &lt;!&ndash; 一体机数据同步线程服务，每1个小时启动一次同步  5 * 60 * 1000&ndash;&gt;-->
<!--    <bean id="tsqXueYadatasync" class="com.htohcloud.controller.quartz.TsqXueYaQuartz" />-->
<!--    &lt;!&ndash; 睡睡康同步&ndash;&gt;-->
<!--    <bean id="sskdatasync" class="com.htohcloud.ssk.datasync.SskDataSyncQuartz" />-->
<!--    &lt;!&ndash; 睡客同步 &ndash;&gt;-->
<!--    <bean id="skdatasync" class="com.htohcloud.ssk.datasync.SkDataSyncQuartz" />-->
<!--    &lt;!&ndash; 国实床垫同步 &ndash;&gt;-->
<!--    <bean id="gszndatasync" class="com.htohcloud.ssk.datasync.GsznDataSyncQuartz" />-->
<!--    &lt;!&ndash; 微信token&ndash;&gt;-->
<!--    <bean id="weixinTokenJob" class="com.htohcloud.weixin.controller.quartz.WeixinTokenJob" />-->

<!--    &lt;!&ndash; sleepart同步&ndash;&gt;-->
<!--    <bean id="sleepartDataSync" class="com.htohcloud.mysleepart.datasync.SleepartDataSyncQuartz" />-->
<!--    &lt;!&ndash; 安防报警同步&ndash;&gt;-->
<!--    <bean id="yjsywarnDataSync" class="com.htohcloud.eavier.controller.quartz.YjsyAlarmSyncQuartz" />-->
<!--    &lt;!&ndash;e家医护一体机数据同步&ndash;&gt;-->
<!--    <bean id="eHomeCareSync" class="com.htohcloud.ehomecare.quartz.EHomeCareSyncQuartz" />-->
<!--    &lt;!&ndash;易键呼 - 信息同步&ndash;&gt;-->
<!--    <bean id="whyxDataQuartz" class="com.htohcloud.bjwhyx.quartzsync.WhyxDataQuartz" />-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    &lt;!&ndash; 易键呼位置信息job的配置开始 &ndash;&gt;-->
<!--    <bean id="WhyxLocationDataSyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="whyxDataQuartz" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>getLocationData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 易键呼位置信息调度的配置开始 &ndash;&gt;-->
<!--    <bean id="whyxLocationJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="WhyxLocationDataSyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            &lt;!&ndash; 每两小时更新一次位置信息&ndash;&gt;-->
<!--            <value>0 0 0/2 * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    &lt;!&ndash; 易键呼状态信息job的配置开始 &ndash;&gt;-->
<!--    <bean id="WhyxDeviceStatuSyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="whyxDataQuartz" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>updataDeviceStatus</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 易键呼状态信息调度的配置开始 &ndash;&gt;-->
<!--    <bean id="whyxDeviceStatuJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="WhyxDeviceStatuSyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            &lt;!&ndash; 每小时更新一次状态信息&ndash;&gt;-->
<!--            <value>0 0 0/2 * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 易键呼呼叫信息job的配置开始 &ndash;&gt;-->
<!--    <bean id="WhyxCallDataSyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="whyxDataQuartz" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>getCallData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 易键呼呼叫信息调度的配置开始 &ndash;&gt;-->
<!--    <bean id="whyxCallJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="WhyxCallDataSyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0/2 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    <bean id="datasyncJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="datasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>syncData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    <bean id="aqgSosDatasyncJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="datasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>syncSosData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="aqgdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="datasyncJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/2 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="aqgsosdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="aqgSosDatasyncJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/1 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="jldatasyncJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="jldatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>jlDataSync</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->



<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="jldatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="jldatasyncJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/5 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; e家医护一体机job的配置开始 &ndash;&gt;-->
<!--    <bean id="eHomeCareSyncJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="eHomeCareSync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>syncData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->



<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="kqdatasyncJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="kqdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>syncData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="kqdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="kqdatasyncJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/5 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="kqdatasyncLocationJobDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="kqdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>getTrackingData</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="kqdatasyncLocationJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="kqdatasyncLocationJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0 0/2 * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="smwsdatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="smwsdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>dataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="smwsdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="smwsdatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/5 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="nursebirthdayhintDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="nursebirthdayhint" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>nurseBirthHintSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="nursebirthdayhintCleanDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="nursebirthdayhint" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>nurseBirthHintCleanSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="nursebirthdayhintJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="nursebirthdayhintDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0/30 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="nursebirthdayhintCleanJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="nursebirthdayhintCleanDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0 23 * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="basesyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="basesync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>baseSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="basesyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="basesyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/2 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始-->
<!--    <bean id="undetectedwarnDetail"-->
<!--        class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="undetectedwarn" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>undetectedWarnSync</value>-->
<!--        </property>-->
<!--    </bean>   &ndash;&gt;-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始Cron表达式“10 */1 * * * ?”意为：从10秒开始，每1分钟执行一次。-->
<!--    <bean id="undetectedwarnJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="undetectedwarnDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/1 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>  &ndash;&gt;-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="alldatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="alldatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>allDataSync</value>-->
<!--        </property>-->
<!--        <property name="concurrent">&lt;!&ndash;配置为false不允许任务并发执行&ndash;&gt;-->
<!--            <value>false</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="alldatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="alldatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0/30 * * * * ? *</value>-->
<!--&lt;!&ndash;            <value>10 0/5 * * * ?</value>&ndash;&gt;-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="ytjdatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="ytjdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>ytjDataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="ytjdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="ytjdatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/3 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="tsqXueYadatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="tsqXueYadatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>tsqXueYaDataSync</value>-->
<!--        </property>-->
<!--        <property name="concurrent" value="false"></property >-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="tsqXueYadatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="tsqXueYadatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0/59 * * * ?</value>  &lt;!&ndash; Cron表达式“10 */1 * * * ?”意为：从10秒开始，每1分钟执行一次。  &ndash;&gt;-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="sskdatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="sskdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>sskDataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 睡客job的配置开始 &ndash;&gt;-->
<!--    <bean id="gszndatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="gszndatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>gsznDataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="gszndatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="gszndatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 0/1 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 睡客job的配置开始 &ndash;&gt;-->
<!--    <bean id="skdatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="skdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>skDataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="skdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="skdatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0/5 * * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 睡客job的配置开始 &ndash;&gt;-->
<!--    <bean id="skdatasyncDetailReport"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="skdatasync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>skDataSyncReport</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->
<!--&lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="skdatasyncReportJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="skdatasyncDetailReport" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0/10 * * * * ?</value>-->
<!--&lt;!&ndash;            <value>0 0 0/3 * * ?</value>&ndash;&gt;-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="weixinTokenDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="weixinTokenJob" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>weixinToken</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="sskdatasyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="sskdatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/3 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="sleepartdatasyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="sleepartDataSync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>sleepartDataSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="sleepartdatasyncJobTrigger" 			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="sleepartdatasyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>10 0/5 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; job的配置开始 &ndash;&gt;-->
<!--    <bean id="yjsywarnDataSyncDetail"-->
<!--          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--        <property name="targetObject">-->
<!--            <ref bean="yjsywarnDataSync" />-->
<!--        </property>-->
<!--        <property name="targetMethod">-->
<!--            <value>yjsyAlarmSync</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="yjsywarnDataSyncJobTrigger" 			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="yjsywarnDataSyncDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            &lt;!&ndash; <value>10 0/5 * * * ?</value>  &ndash;&gt;-->
<!--            <value>0 */1 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->

<!--    <bean id="weixinTokenJobTrigger1" class="org.springframework.scheduling.quartz.SimpleTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="weixinTokenDetail"/>-->
<!--        </property>-->
<!--        <property name="startDelay" value="500" />-->
<!--        <property name="repeatInterval" value="0" />-->
<!--        <property name="repeatCount" value="0" />-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置开始 &ndash;&gt;-->
<!--    <bean id="weixinTokenJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="weixinTokenDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            <value>0 20/90 1-23 * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->

<!--    &lt;!&ndash; e家医护一体机调度的配置开始 &ndash;&gt;-->
<!--    <bean id="eHomeCareSyncJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--        <property name="jobDetail">-->
<!--            <ref bean="eHomeCareSyncJobDetail" />-->
<!--        </property>-->
<!--        <property name="cronExpression">-->
<!--            &lt;!&ndash; <value>0/10 * * * * ?</value>&ndash;&gt;-->
<!--            &lt;!&ndash;每5分钟执行一次&ndash;&gt;-->
<!--            <value>0 0/5 * * * ?</value>-->
<!--        </property>-->
<!--    </bean>-->
<!--    &lt;!&ndash; 调度的配置结束 &ndash;&gt;-->


<!--    &lt;!&ndash; 启动触发器的配置开始 &ndash;&gt;-->
<!--    <bean name="startQuertz" lazy-init="false" autowire="no"-->
<!--          class="org.springframework.scheduling.quartz.SchedulerFactoryBean">-->
<!--        <property name="triggers">-->
<!--            <list>-->
<!--&lt;!&ndash;                <ref bean="alldatasyncJobTrigger" />&ndash;&gt;-->
<!--&lt;!&ndash;                <ref bean="ytjdatasyncJobTrigger" />&ndash;&gt;-->
<!--                &lt;!&ndash;<ref bean="gshtOrderNowDataSyncTaskJobTrigger" />-->
<!--                <ref bean="aqgsosdatasyncJobTrigger" />-->
<!--                <ref bean="whyxCallJobTrigger" />-->
<!--                <ref bean="whyxLocationJobTrigger" />-->
<!--                <ref bean="whyxDeviceStatuJobTrigger" />-->
<!--                <ref bean="eHomeCareSyncJobTrigger" />-->
<!--                <ref bean="yjsywarnDataSyncJobTrigger" />-->
<!--                <ref bean="aqgdatasyncJobTrigger" />-->
<!--                <ref bean="jldatasyncJobTrigger" />-->
<!--                <ref bean="smwsdatasyncJobTrigger" />-->
<!--                <ref bean="nursebirthdayhintJobTrigger" />-->
<!--                <ref bean="nursebirthdayhintCleanJobTrigger" />-->
<!--                <ref bean="kqdatasyncJobTrigger" />-->
<!--                &lt;!&ndash;科强定位信息&ndash;&gt;-->
<!--                <ref bean="kqdatasyncLocationJobTrigger" />-->
<!--                <ref bean="basesyncJobTrigger" />-->
<!--                <ref bean="undetectedwarnJobTrigger" /> &lt;!&ndash;wangml del 20151102 for 注释暂不发布 &ndash;&gt;-->
<!--                <ref bean="sskdatasyncJobTrigger" />-->
<!--                <ref bean="gszndatasyncJobTrigger" />-->
<!--                <ref bean="skdatasyncJobTrigger" />-->
<!--                <ref bean="skdatasyncReportJobTrigger" />-->
<!--                <ref bean="sleepartdatasyncJobTrigger" />-->
<!--                <ref bean="weixinTokenJobTrigger"/>-->
<!--                <ref bean="weixinTokenJobTrigger1"/>-->
<!--                <ref bean="tsqXueYadatasyncJobTrigger" />&ndash;&gt;-->
<!--            </list>-->
<!--        </property>-->
<!--    </bean>-->
    <!-- 启动触发器的配置结束 -->
    <!-- quartz定时配置 end -->
</beans>