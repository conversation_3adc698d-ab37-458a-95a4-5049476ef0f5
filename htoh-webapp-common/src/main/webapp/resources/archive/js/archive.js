var Archive = {
	createNew : function() {
		var cal = Cal.createNew();
		var months;
		//服务器传回的当前月份
		var cloudDate;
		var a = {
				dto:{},
				realdatas : [],
				labels : [],
				changeMonthUrl : "",
				datasize : 0,
				init : function() {
					cloudDate = new Date(this.dto.monthMills);
					months = this.createMonthTime();

					this.labels = new Array();
					this.realdatas = new Array();

					this.fillMonthData();

					this.fillAnalysisData();

					this.initChart();

					this.fillDetailData();
				},
				createMonthTime : function() {
					var d = cal.createFdomDate(this.dto.monthMills);
					var ms = new Array();
					cal.addMonth(-1, d);
					ms[0] = d.Format("yyyyMMdd");
					cal.addMonth(1, d);
					cal.addDay(-1, d);
					ms[1] = d.Format("yyyyMMdd");
					cal.addDay(1, d);
					cal.addMonth(1, d);
					ms[2] = d.Format("yyyyMMdd");
					cal.addMonth(1, d);
					cal.addDay(-1, d);
					ms[3] = d.Format("yyyyMMdd");
					return ms;
				},

				throttle : function(fn, delay) {
					var timer = null;
					return function() {
						var context = this, args = arguments;
						clearTimeout(timer);
						timer = setTimeout(function() {
							fn.apply(context, args);
						}, delay);
					};
				},
				
				fillMonthData : function() {
					var _self = this;
					var nowDate = new Date();
					var objEvt = $._data($("#next_month_span")[0], "events");
					var isBind = (objEvt && objEvt["click"]);
					$("#month_time_span").html(cloudDate.Format("yyyy年MM月"));
					if (cloudDate.getMonth() == nowDate.getMonth()
							&& cloudDate.getFullYear() == nowDate.getFullYear()) {
						$("#next_month_span").css("color", "#a0a0a0");
						if (isBind) {
							$('#next_month_span').unbind("click");
						}
					} else {
						$("#next_month_span").css("color", "#20a0f8");
						if (!isBind) {
							$('#next_month_span').click(function() {
								_self.changeMonth(true);
							});
						}
					}
				},
				
				initPanel : function () {
					$('#trend_panel').panel({
						title : '趋势分析',
						cls : 'archive_panel',
						headerCls : 'archive_panel_header',
						bodyCls : 'archive_panel_body',
						onResize : function() {
							a.initChart();
						},
					});
					
					$('#detail_panel').panel({
						title : '详细数据',
						cls : 'archive_panel',
						headerCls : 'archive_panel_header',
						bodyCls : 'archive_panel_body',
					});
					
					$('#data_panel').panel({
						title : '数据分析',
						cls : 'archive_panel',
						headerCls : 'archive_panel_header',
						bodyCls : 'archive_panel_body',
					});
				},
				
				fixScroller : function (divid, fn) {
					var oldwid = $(divid).width();
					fn();
					if (oldwid && oldwid != $(divid).width()) {
						fn();
					}
				},
				
				changeMonth : function (next) {
					var _self = this;
					var url = this.changeMonthUrl
							+ this.dto.uid;
					if (next) {
						url = url + "&startDate=" + months[2];
						url = url + "&endDate=" + months[3];
					} else {
						url = url + "&startDate=" + months[0];
						url = url + "&endDate=" + months[1];
					}
					$.ajax({
						url : url,
						async : true,
						dataTpye : 'json',
						success : function(data) {
							if (data.status == "success") {

								_self.dto = data.results;
								_self.init();
								_self.initPanel();
							} else {
								alert("获取数据失败");
							}
						}
					});
				},
				
				dealDatas : function (){},
				fillAnalysisData : function (){},
				initChart : function (){},
				fillDetailData : function (){},
		};
			
		return a;
	}
		
}
