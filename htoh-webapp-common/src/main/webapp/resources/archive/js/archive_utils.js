var ArchiveUtils = {
	createMonthTime : function(cal, dateTime) {
		var d = cal.createFdomDate(dateTime);
		var ms = new Array();
		cal.addMonth(-1, d);
		ms[0] = d.Format("yyyyMMdd");
		cal.addMonth(1, d);
		cal.addDay(-1, d);
		ms[1] = d.Format("yyyyMMdd");
		cal.addDay(1, d);
		cal.addMonth(1, d);
		ms[2] = d.Format("yyyyMMdd");
		cal.addMonth(1, d);
		cal.addDay(-1, d);
		ms[3] = d.Format("yyyyMMdd");
		return ms;
	},

	throttle : function(fn, delay) {
		var timer = null;
		return function() {
			var context = this, args = arguments;
			clearTimeout(timer);
			timer = setTimeout(function() {
				fn.apply(context, args);
			}, delay);
		};
	},
	
	fillMonthData : function(d) {
		var nowDate = new Date();
		var objEvt = $._data($("#next_month_span")[0], "events");
		var isBind = (objEvt && objEvt["click"]);
		$("#month_time_span").html(d.Format("yyyy年MM月"));
		if (d.getMonth() == nowDate.getMonth()
				&& d.getFullYear() == nowDate.getFullYear() && isBind) {
			$("#next_month_span").css("color", "#a0a0a0");
			$('#next_month_span').unbind("click");
		} else {
			if (!isBind) {
				$("#next_month_span").css("color", "#20a0f8");
				$('#next_month_span').click(function() {
					changeMonth(true);
				});
			}
		}
	},
	
	initPanel : function () {
		$('#trend_panel').panel({
			title : '趋势分析',
			cls : 'archive_panel',
			headerCls : 'archive_panel_header',
			bodyCls : 'archive_panel_body',
			onResize : function() {
				ArchiveUtils.initChart();
			},
		});
		
		$('#detail_panel').panel({
			title : '详细数据',
			cls : 'archive_panel',
			headerCls : 'archive_panel_header',
			bodyCls : 'archive_panel_body',
		});
		
		$('#data_panel').panel({
			title : '数据分析',
			cls : 'archive_panel',
			headerCls : 'archive_panel_header',
			bodyCls : 'archive_panel_body',
		});
	},
	
	fixScroller : function (divid, fn) {
		var oldwid = $(divid).width();
		fn();
		if (oldwid && oldwid != $(divid).width()) {
			fn();
		}
	},
	
	initChart : function () {}, 
}
