<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=11;IE=10;IE=9; IE=8; IE=7; IE=EDGE">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="cache-control" content="no-cache" />
<meta http-equiv="expires" content="0" />

<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/resources/easyui/themes/default/easyui.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/resources/easyui/themes/icon.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/resources/easyui/demo/demo.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/resources/easyui/IconExtension.css" />
		
<script type="text/javascript" src="<%=request.getContextPath()%>/resources/easyui/jquery.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/resources/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/resources/easyui/locale/easyui-lang-zh_CN.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/resources/easyui/sys.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/resources/js/innopro/dooruserdevicebind/show.js"></script>
<style type="text/css">
::-ms-clear, ::-ms-reveal{display: none;}
</style>
<title>基础档案</title>
<script type="text/javascript">
var pageRows=10;
var baseUrl="<%=request.getContextPath()%>";
var provinceList=${provinceList};
var city=${city};
</script>
</head>
<body class="easyui-layout" style="overflow: auto;">
<input type="hidden" value="<%=request.getContextPath()%>" id="ctx">
	<div data-options="region:'north'" style="height:130px;background:#f1f7fd;margin-bottom:5px;border-top:none;border-left:none;border-right:none;" class="north">
<table style="margin-top:13px;margin-left:20px;">
		<tr>
			<td style="text-align: right;"><span>&nbsp;&nbsp;&nbsp;户籍地址：</span></td>
	<td colspan="7" ><input type="text" id="province" name="province" value="${city1.province_id}"class="easyui-combobox" prompt="请选择省份" style="width: 120px;">&nbsp;
			<input type="text" id="city" name="city" class="easyui-combobox" value="${city1.city_id}" prompt="请选择城市"  style="width: 120px;">&nbsp;
			<input type="text" id="county" name="county" class="easyui-combobox" prompt="请选择区/县" value="${city1.county_id}" style="width: 120px;">&nbsp;
			<input type="text" id="town" name="town" class="easyui-combobox"  prompt="请选择街道(镇)"  style="width: 120px;">
			<input type="text" id="village" name="village" class="easyui-combobox"   prompt="请选择社区(村)"  style="width: 120px;"></td>
			</tr>
		<tr>
			<td style="text-align: right;">&nbsp;&nbsp;&nbsp;姓名：</td>
			<td><input id="name" class="easyui-textbox" prompt="请输入姓名"/></td>
			<td style="text-align: right;">手机号：</td>
			<td><input id="phoneNum" class="easyui-textbox"  prompt="请输入手机号"/></td>
			<td style="text-align: right;">设备编号：</td>
			<td><input id="devId" class="easyui-textbox"  prompt="请输入设备编号"/></td>
			<td>
				&nbsp;&nbsp; 
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-2012092109942" plain="true" id="oldiebasesearch">查询</a>
			</td>
		</tr>
	</table>
		<div style="float: right;margin-top:-33px;margin-right:15px;">
					<a   href="javascript:void(0);" class="easyui-linkbutton" iconCls="loadExcle" plain="true" id="loadExcle">导出Excel</a>
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" plain="true"  id="toaddoldie" >新增</a>
		</div>
	</div>
	<div id="tabs"  style="border: 0px;margin-left:5px;overflow: auto;" data-options="region:'center'">
	<table id="oldietable" style="width: auto;" class="easyui-datagrid" 
	   data-options="singleSelect:true,fitColumns:true,fit:true,pagination:true, onRowContextMenu: function(e, rowIndex, rowData) { //右键时触发事件//三个参数：e里面的内容很多，rowIndex就是当前点击时所在行的索引，rowData当前行的数据
           		e.preventDefault(); //阻止浏览器捕获右键事件  
           		 $(this).datagrid('clearSelections'); //取消所有选中项
            	$(this).datagrid('selectRow', rowIndex); //根据索引选中该行
            	$('#mm').menu('show', {//显示右键菜单
                left: e.pageX,//在鼠标点击处显示菜单
                top: e.pageY});}">
		<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'id',hidden:true"></th>
				<th data-options="field:'clinetNO',width:1,align:'center'">老人编号</th>
				<th data-options="field:'clinetName',width:1,align:'center'">姓名</th>
					<th data-options="field:'sex',width:1,align:'center',formatter:function(value,row,index){
							if(value==1){return '女';}else if(value==0){return '男';}}">性别</th>
				<th data-options="field:'age',width:1,align:'center'">年龄</th>
				<th data-options="field:'telephone',width:1,align:'center'">固定电话</th>
				<th data-options="field:'phoneNum',width:1,align:'center'">手机号</th>
				<th data-options="field:'idCard',width:2,align:'center'">证件号码</th>
				<th data-options="field:'realBirthday',width:1,align:'center'">出生日期</th>
				<th data-options="field:'householdAddr',width:2,align:'center'">户籍地址</th>
				<th data-options="field:'live',width:1,align:'center',formatter:function(value,row,index){
							if(value==1){return '是';}else if(value==0){return '否';}}">是否健在</th>
				<th data-options="field:'操作',width:1,align:'center', formatter:opFormatter">操作</th>
			</tr>
		</thead>
	</table>
	</div>
	
	<div id="mm" class="easyui-menu" data-options="onClick:menuHandler" style="width: 80px;">
    		<div data-options="name:'查看',iconCls:'icon-chakan'" style="background: #f8f8f8;">查看</div>
    		<div data-options="name:'新增',iconCls:'icon-adds'" style="background: #f8f8f8;">新增</div>  
    		<div data-options="name:'修改',iconCls:'icon-edits'" style="background: #f8f8f8;">修改</div>
		</div> 
</body>
</html>