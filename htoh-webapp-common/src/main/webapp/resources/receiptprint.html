<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>会员消费小票</title>
<!-- 控件跨浏览器调用类库--> 
<script type="text/javascript" src="js/LodopFuncs.js"></script>
<script type="text/javascript" src="js/receiptprint.js"></script>
<script type="text/javascript">
	var json = {
		"title":"医家通会员订单信息", 
		"realName":"王小明", 
		"phoneNum": "13831893603", 
		"serialNO":"TSYL20151222000001",
		"operateDate": "2015-11-27 15:30:15",   
		"cardId": "12345678", 
		"orgName":"天思养老", 
		"tradeType":"消费",
		"paymentType":"现金",
		"total":"1000.00",
		"totalCount":1,  
		"goodsList":[  
			{"itemName":"理疗", "price":"1000.00", "count":1, "total":"1000.00"} 
		]
	}; 
	
	function printPreview(json){  
		//创建小票打印页  
		CreatePrintPage(json);  
		//打印预览  
		LODOP.PREVIEW();      
	}  
	
	function printOrder(json) {  
	          
	    //创建小票打印页  
	    CreatePrintPage(json);  
	    //开始打印  
	    LODOP.PRINT();    
	      
	};

</script>

<!-- 调用LODOP控件代码-->  
<object id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>  
  <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0 pluginspage="install_lodop.exe"></embed>  
</object>  
</head>  
<body>  
<input type="button" value="打印预览" onclick="printPreview()" />  
<!-- 控制打印按钮,自动打印可以直接执行printOrder-->  
<input type="button" value="直接打印" onclick="printOrder()" />
</body>  
</html>  