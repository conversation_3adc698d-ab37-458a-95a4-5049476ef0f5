<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="content-type" content="text/html; charset=utf-8" />
		<title>xhEditor demos</title>
		<style type="text/css" rel="stylesheet">
			body {
				font-size: 12px;
				font-family: Courier New;
			}
			li {
				margin: 5px;
			}
		</style>
	</head>
	<body>
		<h4>xhEditor演示程序列表</h4>
		<ol>
			<li><a href="demo01.html" target="_blank">demo01.html</a> (默认模式)</li>
			<li><a href="demo02.html" target="_blank">demo02.html</a> (自定义按钮)</li>
			<li><a href="demo03.html" target="_blank">demo03.html</a> (皮肤选择)</li>
			<li><a href="demo04.html" target="_blank">demo04.html</a> (其它选项)</li>
			<li><a href="demo05.html" target="_blank">demo05.html</a> (Javascript API交互)</li>
			<li><a href="demo06.html" target="_blank">demo06.html</a> (非utf-8编码网页调用)</li>
			<li><a href="demo07.html" target="_blank">demo07.html</a> (UBB可视化编辑)</li>
			<li><a href="demo08.html" target="_blank">demo08.html</a> (Ajax文件上传)</li>
			<li><a href="demo09.html" target="_blank">demo09.html</a> (插件扩展)</li>
			<li><a href="demo10.html" target="_blank">demo10.html</a> (iframe调用文件上传)</li>
			<li><a href="demo11.html" target="_blank">demo11.html</a> (异步加载)</li>
			<li><a href="demo12.html" target="_blank">demo12.html</a> (远程抓图)</li>
		</ol>
	</body>
</html>
