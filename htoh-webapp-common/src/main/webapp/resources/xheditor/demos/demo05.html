<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>xhEditor demo5 : Javascript API交互</title>
<link rel="stylesheet" href="common.css" type="text/css" media="screen" />
<script type="text/javascript" src="../jquery/jquery-1.4.4.min.js"></script>
<script type="text/javascript" src="../xheditor-1.1.14-zh-cn.min.js"></script>
<script type="text/javascript">
$(pageInit);
var editor;
function pageInit()
{
	editor=$('#elm1').xheditor({shortcuts:{'ctrl+enter':submitForm}});//交互方式1
	editor=$('#elm1')[0].xheditor;//交互方式2
}
function submitForm(){$('#frmDemo').submit();}
</script>
</head>
<body>
<div id="header-nav">
	<ul>
		<li><a href="demo01.html"><span>默认模式</span></a></li>
		<li><a href="demo02.html"><span>自定义按钮</span></a></li>
		<li><a href="demo03.html"><span>皮肤选择</span></a></li>
		<li><a href="demo04.html"><span>其它选项</span></a></li>
		<li><a href="demo05.html"><span>API交互</span></a></li>
		<li><a href="demo06.html"><span>非utf-8编码调用</span></a></li>
		<li><a href="demo07.html"><span>UBB可视化</span></a></li>
		<li><a href="demo08.html"><span>Ajax上传</span></a></li>
		<li><a href="demo09.html"><span>插件扩展</span></a></li>
		<li><a href="demo10.html"><span>iframe调用上传</span></a></li>
		<li><a href="demo11.html"><span>异步加载</span></a></li>
		<li><a href="demo12.html"><span>远程抓图</span></a></li>
		<li><a href="../wizard.html" target="_blank"><span>生成代码</span></a></li>
	</ul>
</div>
<form id="frmDemo" method="post" action="show.php">
	<h3>xhEditor demo5 : Javascript API交互</h3>
	<textarea id="elm1" name="elm1" rows="12" cols="80" style="width: 80%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;var editor;&lt;br /&gt;editor=$('#elm1').xheditor();//方式1&lt;br /&gt;editor=$('#elm1')[0].xheditor;//方式2&lt;br /&gt;editor.pasteHTML('&amp;lt;strong&amp;gt;粘贴的内容&amp;lt;/strong&amp;gt;');&lt;/p&gt;&lt;p&gt;&lt;br /&gt;注：本页面演示大部分外部调用的API接口，更多详细帮助信息请查看：&lt;a href=&quot;http://xheditor.com/manual/2#chapter3&quot;&gt;http://xheditor.com/manual/2#chapter3&lt;/a&gt;&lt;/p&gt;
	</textarea>
	<br /><br />
	<input type="submit" name="save" value="Submit" />
	<input type="reset" name="reset" value="Reset" />
</form>
<br/>
<div style="border:1px solid #999;padding:10px;">
<a href="javascript:;" onclick="editor.focus();return false;">focus()</a> | 
<a href="javascript:;" onclick="editor.setSource('<p>aaa1</p>');return false;">setSource('&lt;p&gt;aaa1&lt;/p&gt;')</a> |
<a href="javascript:;" onclick="$('#elm1').val('<p>aaa2</p>');return false;">$('#elm1').val('&lt;p&gt;aaa2&lt;/p&gt;')</a> |
<a href="javascript:;" onclick="alert(editor.getSource());return false;">getSource()</a> | 
<a href="javascript:;" onclick="alert($('#elm1').val());return false;">$('#elm1').val()</a> |
<a href="javascript:;" onclick="editor.appendHTML('<strong>添加在尾部</strong>');return false;">appendHTML('&lt;strong&gt;添加在尾部&lt;/strong&gt;')</a> | 
<a href="javascript:;" onclick="alert(editor.getSelect());return false;">getSelect()</a> | 
<a href="javascript:;" onclick="alert(editor.getSelect('text'));return false;">getSelect('text')</a> | 
<a href="javascript:;" onclick="editor.pasteHTML('<strong>粘贴的内容</strong>');return false;">pasteHTML('&lt;strong&gt;粘贴的内容&lt;/strong&gt;')</a> | 
<a href="javascript:;" onclick="editor.pasteText('<strong>粘贴的内容</strong>');return false;">pasteText('&lt;strong&gt;粘贴的内容&lt;/strong&gt;')</a> | 
<a href="javascript:;" onclick="alert(editor.formatXHTML('<b>abc</b>'));return false;">formatXHTML('&lt;b&gt;abc&lt;/b&gt;')</a> | 
<a href="javascript:;" onclick="editor.toggleSource();return false;">toggleSource()</a> | 
<a href="javascript:;" onclick="editor.toggleSource(true);return false;">toggleSource(true)</a> | 
<a href="javascript:;" onclick="editor.toggleFullscreen(true);return false;">toggleFullscreen(true)</a> |  
<a href="javascript:;" onclick="editor.toggleShowBlocktag();return false;">toggleShowBlocktag()</a> | 
<a href="javascript:;" onclick="alert(editor.settings.upLinkExt);return false;">alert(settings.upLinkExt)</a> | 
<a href="javascript:;" onclick="editor.exec('About');return false;">exec('About')</a>
</div>
</body>
</html>