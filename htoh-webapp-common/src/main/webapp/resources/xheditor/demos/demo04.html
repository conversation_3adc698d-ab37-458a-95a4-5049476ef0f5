<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>xhEditor demo4 : 其它选项</title>
<link rel="stylesheet" href="common.css" type="text/css" media="screen" />
<script type="text/javascript" src="../jquery/jquery-1.4.4.min.js"></script>
<script type="text/javascript" src="../xheditor-1.1.14-zh-cn.min.js"></script>
<script type="text/javascript">
$(pageInit);
function pageInit()
{
	$.extend(xheditor.settings,{shortcuts:{'ctrl+enter':submitForm}});
	$('#elm1').xheditor({urlType:'rel'});
	$('#elm2').xheditor({urlType:'root'});
	$('#elm3').xheditor({urlType:'abs'});
	$('#elm4').xheditor({urlBase:'img/'});
	var emots={
		msn:{name:'MSN',count:40,width:22,height:22,line:8},
		pidgin:{name:'Pidgin',width:22,height:25,line:8,list:{smile:'微笑',cute:'可爱',wink:'眨眼',laugh:'大笑',victory:'胜利',sad:'伤心',cry:'哭泣',angry:'生气',shout:'大骂',curse:'诅咒',devil:'魔鬼',blush:'害羞',tongue:'吐舌头',envy:'羡慕',cool:'耍酷',kiss:'吻',shocked:'惊讶',sweat:'汗',sick:'生病',bye:'再见',tired:'累',sleepy:'睡了',question:'疑问',rose:'玫瑰',gift:'礼物',coffee:'咖啡',music:'音乐',soccer:'足球',good:'赞同',bad:'反对',love:'心',brokenheart:'伤心'}},
		ipb:{name:'IPB',width:20,height:25,line:8,list:{smile:'微笑',joyful:'开心',laugh:'笑',biglaugh:'大笑',w00t:'欢呼',wub:'欢喜',depres:'沮丧',sad:'悲伤',cry:'哭泣',angry:'生气',devil:'魔鬼',blush:'脸红',kiss:'吻',surprised:'惊讶',wondering:'疑惑',unsure:'不确定',tongue:'吐舌头',cool:'耍酷',blink:'眨眼',whistling:'吹口哨',glare:'轻视',pinch:'捏',sideways:'侧身',sleep:'睡了',sick:'生病',ninja:'忍者',bandit:'强盗',police:'警察',angel:'天使',magician:'魔法师',alien:'外星人',heart:'心动'}}
	};
	$('#elm5').xheditor({tools:'full',skin:'default',width:800,height:200,clickCancelDialog:false,fullscreen:false,layerShadow:3,linkTag:false,cleanPaste:0,defLinkText:'默认超链接文字',sourceMode:false,showBlocktag:true,forcePtag:false,internalScript:true,inlineScript:true,internalStyle:true,inlineStyle:true,hoverExecDelay:-1,loadCSS:'http://xheditor.com/css/common.css',emots:emots});
}
function submitForm(){$('#frmDemo').submit();}
</script>
</head>
<body>
<div id="header-nav">
	<ul>
		<li><a href="demo01.html"><span>默认模式</span></a></li>
		<li><a href="demo02.html"><span>自定义按钮</span></a></li>
		<li><a href="demo03.html"><span>皮肤选择</span></a></li>
		<li><a href="demo04.html"><span>其它选项</span></a></li>
		<li><a href="demo05.html"><span>API交互</span></a></li>
		<li><a href="demo06.html"><span>非utf-8编码调用</span></a></li>
		<li><a href="demo07.html"><span>UBB可视化</span></a></li>
		<li><a href="demo08.html"><span>Ajax上传</span></a></li>
		<li><a href="demo09.html"><span>插件扩展</span></a></li>
		<li><a href="demo10.html"><span>iframe调用上传</span></a></li>
		<li><a href="demo11.html"><span>异步加载</span></a></li>
		<li><a href="demo12.html"><span>远程抓图</span></a></li>
		<li><a href="../wizard.html" target="_blank"><span>生成代码</span></a></li>
	</ul>
</div>
<form id="frmDemo" method="post" action="show.php">
	<h3>xhEditor demo4 : 其它选项</h3>
	1,本地URL转相对地址(urlType:rel):<br />
	<textarea id="elm1" name="elm1" rows="8" cols="80" style="width: 80%">
&lt;p&gt;内部图片：&lt;img src=&quot;img/xheditor.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;外部图片：&lt;img src=&quot;http://www.google.cn/intl/zh-CN/images/logo_cn.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;内部链接：&lt;a href=&quot;index.html&quot;&gt;xhEditor&lt;/a&gt;&lt;/p&gt;&lt;p&gt;外部链接：&lt;a href=&quot;http://www.google.com/&quot;&gt;Google&lt;/a&gt;&lt;/p&gt;&lt;p&gt;&nbsp;&lt;/p&gt;&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm1').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;urlType:'rel'&lt;/span&gt;});&lt;br /&gt;&lt;/p&gt;
	</textarea><br /><br />
	2,本地URL转根地址(urlType:root):<br />
	<textarea id="elm2" name="elm2" rows="8" cols="80" style="width: 80%">
&lt;p&gt;内部图片：&lt;img src=&quot;img/xheditor.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;外部图片：&lt;img src=&quot;http://www.google.cn/intl/zh-CN/images/logo_cn.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;内部链接：&lt;a href=&quot;index.html&quot;&gt;xhEditor&lt;/a&gt;&lt;/p&gt;&lt;p&gt;外部链接：&lt;a href=&quot;http://www.google.com/&quot;&gt;Google&lt;/a&gt;&lt;/p&gt;&lt;p&gt;&nbsp;&lt;/p&gt;&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm2').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;urlType:'root'&lt;/span&gt;});&lt;br /&gt;&lt;/p&gt;
	</textarea><br /><br />
	3,本地URL转绝对地址(urlType:abs):<br />
	<textarea id="elm3" name="elm3" rows="8" cols="80" style="width: 80%">
&lt;p&gt;内部图片：&lt;img src=&quot;img/xheditor.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;外部图片：&lt;img src=&quot;http://www.google.cn/intl/zh-CN/images/logo_cn.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;内部链接：&lt;a href=&quot;index.html&quot;&gt;xhEditor&lt;/a&gt;&lt;/p&gt;&lt;p&gt;外部链接：&lt;a href=&quot;http://www.google.com/&quot;&gt;Google&lt;/a&gt;&lt;/p&gt;&lt;p&gt;&nbsp;&lt;/p&gt;&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm3').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;urlType:'abs'&lt;/span&gt;});&lt;br /&gt;&lt;/p&gt;
	</textarea><br /><br />
	4,相对地址的基址路径(urlBase):<br />
	<textarea id="elm4" name="elm4" rows="8" cols="80" style="width: 80%">
&lt;p&gt;图片：&lt;img src=&quot;xheditor.gif&quot; alt=&quot;&quot; /&gt;&lt;/p&gt;&lt;p&gt;链接：&lt;a href=&quot;xheditor.gif&quot;&gt;xhEditor&lt;/a&gt;&lt;/p&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm4').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;urlBase:'img/'&lt;/span&gt;});&lt;/p&gt;&lt;p&gt;urlBase主要应用在前后台路径不一致的情况下，举例如下：&lt;/p&gt;&lt;p&gt;前台文章地址为：http://xheditor.com/read/1.html&lt;br /&gt;后台发布文章地址为：http://xheditor.com/admin/add.php&lt;/p&gt;&lt;p&gt;针对这种情况，使用urlType:root，或者urlType:abs当然能解决此问题，但是这样的路径一方面过长，另一方面也不利于今后调整路径结构。&lt;/p&gt;&lt;p&gt;本质来讲，这个参数的功能和HTML中的&amp;lt;base href=&quot;&quot;&amp;gt;功能是一致的。&lt;/p&gt;&lt;p&gt;目标就是让文章中的图片保存为以文章发布路径为基础的相对路径，但是在后台编辑文章时又不至于显示不了图片。&lt;br /&gt;&lt;/p&gt;
	</textarea><br /><br />
	5,其它更多选项:<br />
	<textarea id="elm5" name="elm5" rows="8" cols="80" style="width: 80%;background:url(img/xheditorbg.gif) no-repeat right bottom fixed">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm5').xheditor({tools:'full',skin:'default',width:800,height:200,clickCancelDialog:false,fullscreen:false,layerShadow:3,linkTag:false,cleanPaste:0,defLinkText:'默认超链接文字',sourceMode:false,showBlocktag:true,forcePtag:false,internalScript:true,inlineScript:true,internalStyle:true,inlineStyle:true,hoverExecDelay:-1,loadCSS:'http://xheditor.com/css/global.css',emots:{msn:{name:'MSN',count:40,width:22,height:22,line:8}},shortcuts:{'ctrl+enter':submitForm}});&lt;/p&gt;&lt;br /&gt;&lt;br /&gt;注：本实例演示其它大部分的初始化参数，详细帮助信息请查看：&lt;a href="http://xheditor.com/manual/2#chapter2"&gt;http://xheditor.com/manual/2#chapter2&lt;/a&gt;
	</textarea>
	<br/><br />
	<input type="submit" name="save" value="Submit" />
	<input type="reset" name="reset" value="Reset" />
</form>
</body>
</html>