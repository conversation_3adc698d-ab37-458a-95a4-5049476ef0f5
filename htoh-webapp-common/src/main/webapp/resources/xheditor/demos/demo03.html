<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>xhEditor demo3 : 皮肤选择</title>
<link rel="stylesheet" href="common.css" type="text/css" media="screen" />
<script type="text/javascript" src="../jquery/jquery-1.4.4.min.js"></script>
<script type="text/javascript" src="../xheditor-1.1.14-zh-cn.min.js"></script>
<script type="text/javascript">
$(pageInit);
function pageInit()
{
	$.extend(xheditor.settings,{shortcuts:{'ctrl+enter':submitForm}});
	$('#elm1').xheditor({skin:'default'});
	$('#elm2').xheditor({skin:'o2007blue'});
	$('#elm3').xheditor({skin:'o2007silver'});
	$('#elm4').xheditor({skin:'vista'});
	$('#elm5').xheditor({skin:'nostyle'});
}
function submitForm(){$('#frmDemo').submit();}
</script>
</head>
<body>
<div id="header-nav">
	<ul>
		<li><a href="demo01.html"><span>默认模式</span></a></li>
		<li><a href="demo02.html"><span>自定义按钮</span></a></li>
		<li><a href="demo03.html"><span>皮肤选择</span></a></li>
		<li><a href="demo04.html"><span>其它选项</span></a></li>
		<li><a href="demo05.html"><span>API交互</span></a></li>
		<li><a href="demo06.html"><span>非utf-8编码调用</span></a></li>
		<li><a href="demo07.html"><span>UBB可视化</span></a></li>
		<li><a href="demo08.html"><span>Ajax上传</span></a></li>
		<li><a href="demo09.html"><span>插件扩展</span></a></li>
		<li><a href="demo10.html"><span>iframe调用上传</span></a></li>
		<li><a href="demo11.html"><span>异步加载</span></a></li>
		<li><a href="demo12.html"><span>远程抓图</span></a></li>
		<li><a href="../wizard.html" target="_blank"><span>生成代码</span></a></li>
	</ul>
</div>
<form id="frmDemo" method="post" action="show.php">
	<h3>xhEditor demo3 : 皮肤选择</h3>
	1,默认皮肤:<br/>
	<textarea id="elm1" name="elm1" rows="12" cols="80" style="width: 90%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm1').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;skin:'default'&lt;/span&gt;});&lt;/p&gt;
	</textarea><br /><br />
	2,Office 2007 蓝色:<br />
	<textarea id="elm2" name="elm2" rows="12" cols="80" style="width: 90%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm2').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;skin:'o2007blue'&lt;/span&gt;});&lt;/p&gt;
	</textarea><br /><br />
	3,Office 2007 银白色:<br />
	<textarea id="elm3" name="elm3" rows="12" cols="80" style="width: 90%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm3').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;skin:'o2007silver'&lt;/span&gt;});&lt;/p&gt;
	</textarea><br /><br />
	4,Vista:<br />
	<textarea id="elm4" name="elm4" rows="12" cols="80" style="width: 90%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm4').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;skin:'vista'&lt;/span&gt;});&lt;/p&gt;
	</textarea><br /><br />
	5,NoStyle:<br />
	<textarea id="elm5" name="elm5" rows="12" cols="80" style="width: 90%">
&lt;p&gt;当前实例调用的Javascript源代码为：&lt;/p&gt;&lt;p&gt;$('#elm5').xheditor({&lt;span style=&quot;color:#ff0000;&quot;&gt;skin:'nostyle'&lt;/span&gt;});&lt;/p&gt;&lt;p&gt;皮肤作者：&lt;strong&gt;shiny&lt;/strong&gt; (dev.meettea.com)&lt;/p&gt;
	</textarea>
	<br/><br />注：为了保持项目精简，同一个页面只能调用一个皮肤，当同一界面同时调用多个皮肤时，最后一个皮肤的按钮面板样式会影响之前的<br /><br />
	<input type="submit" name="save" value="Submit" />
	<input type="reset" name="reset" value="Reset" />
</form>
</body>
</html>