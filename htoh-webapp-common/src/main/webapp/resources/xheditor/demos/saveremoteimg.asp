<%@ codepage=65001%><%
' saveremoteimg demo for asp
' @requires xhEditor
' 
' <AUTHOR>
' @site http://xheditor.com/
' @licence LGPL(http://www.opensource.org/licenses/lgpl-license.php)
' 
' @Version: 0.9.1 (build 110703)
'
' 注：本程序仅为演示用，只实现了最简单的远程抓图及粘贴上传，如果要完善此功能，还需要自行开发以下功能：
'		1，非图片扩展名的URL地址抓取
'		2，大体积的图片转jpg格式，以及加水印等后续操作
'		3，上传记录存入数据库以管理用户上传图片
'
'option explicit

response.charset="UTF-8"

	dim attachDir,dirType,maxAttachSize,upExt
	attachDir="upload"'上传文件保存路径，结尾不要带/
	dirType=1'1:按天存入目录 2:按月存入目录 3:按扩展名存目录  建议使用按天存
	maxAttachSize=2097152'最大上传大小，默认是2M
	upExt="jpg,jpeg,gif,png"'上传扩展名

	'------------------------------------------------------------
	
	upExt=","&upExt&","
	dim arrUrl,i,localUrl,filesize
	arrUrl=split(request("urls"),"|")
	for i=0 to Ubound(arrUrl)
		localUrl=saveRemoteImg(arrUrl(i))
		if localUrl<>"" then arrUrl(i)=localUrl
	next
	response.write Join(arrUrl,"|")
	response.end
	
	
	'保存远程文件
	Function saveRemoteImg(sUrl)
		dim fileContent,objStream,sExt,sFile
		if left(sUrl,10)="data:image" then'base64编码的图片，可能出现在firefox粘贴，或者某些网站上，例如google图片
			dim pstart
			pstart=instr(sUrl,"/")+1
			sExt=LCase(mid(sUrl,pstart,instr(sUrl,";")-pstart))
			if not cbool(InStr(1,upExt,","&sExt&",")) then exit function
			fileContent=DecodeBase64(mid(sUrl,instr(sUrl,"base64,")+7))
		else
			sExt=LCase(Mid(sUrl,InStrRev(sUrl, ".")+1))
			if not cbool(InStr(1,upExt,","&sExt&",")) then exit function
			fileContent=getUrl(sUrl)
		end if
		if LenB(fileContent)>maxAttachSize then exit function'文件体积超过最大限制
		sFile=getLocalPath(sExt)
		Set objStream = Server.CreateObject("ADODB.Stream")
		objStream.Type =1
		objStream.Open
		objstream.write fileContent
		objstream.SaveToFile server.mappath(sFile),2
		objstream.Close()
		set objstream=nothing
		saveRemoteImg=sFile
	End Function
	'反编码base64
	Function DecodeBase64(str)
		With CreateObject("Microsoft.XMLDOM").createElement("TXT")
			.dataType = "bin.base64"
			.text = str
			DecodeBase64 = .nodeTypedValue
		End With
	End Function
	'抓到远程URL内容
	Function getUrl(sUrl)
		dim http
		Set http = Server.CreateObject("MSXML2.ServerXMLHTTP")
		http.open "GET",sUrl,false
		http.setRequestHeader "Accept","*/*"
		http.setRequestHeader "User-Agent","Googlebot/2.1(+http://www.google.com/bot.html)"
		http.send()
		if http.readystate<>4 then
			getUrl=""
			exit function   
		end if
		getUrl=http.responseBody
	End Function
	'返回本地文件路径
	Function getLocalPath(extension)
		dim attach_dir,attach_subdir,filename,target,tmpfile
		select case dirType
			case 1
				attach_subdir="day_"+DateFormat(now,"yymmdd")
			case 2
				attach_subdir="month_"+DateFormat(now,"yymm")
			case 3
				attach_subdir="ext_"+extension
		end select
		attach_dir=attachDir+"/"+attach_subdir+"/"
		CreateFolder attach_dir
		Randomize timer
		filename=DateFormat(now,"yyyymmddhhnnss")+cstr(cint(9999*Rnd))+"."+extension
		getLocalPath=attach_dir+filename
	End Function
	'iif函数
	Function iif(expression,returntrue,returnfalse)
		If expression=true Then
			iif=returntrue
		Else
			iif=returnfalse
		End If
	End Function
	'格式日期
	Function DateFormat(strDate,fstr)
		if isdate(strDate) then
			dim i,temp
			temp=replace(fstr,"yyyy",DatePart("yyyy",strDate))
			temp=replace(temp,"yy",mid(DatePart("yyyy",strDate),3))
			temp=replace(temp,"y",DatePart("y",strDate))
			temp=replace(temp,"w",DatePart("w",strDate))
			temp=replace(temp,"ww",DatePart("ww",strDate))
			temp=replace(temp,"q",DatePart("q",strDate))
			temp=replace(temp,"mm",iif(len(DatePart("m",strDate))>1,DatePart("m",strDate),"0"&DatePart("m",strDate)))
			temp=replace(temp,"dd",iif(len(DatePart("d",strDate))>1,DatePart("d",strDate),"0"&DatePart("d",strDate)))
			temp=replace(temp,"hh",iif(len(DatePart("h",strDate))>1,DatePart("h",strDate),"0"&DatePart("h",strDate)))
			temp=replace(temp,"nn",iif(len(DatePart("n",strDate))>1,DatePart("n",strDate),"0"&DatePart("n",strDate)))
			temp=replace(temp,"ss",iif(len(DatePart("s",strDate))>1,DatePart("s",strDate),"0"&DatePart("s",strDate)))
			DateFormat=temp
		else
			DateFormat=false
		end if
	End Function
	'新建文件夹
	Function CreateFolder(FolderPath)
		dim lpath,fs,f
	  lpath=Server.MapPath(FolderPath)
	  Set fs=Server.CreateObject("Scri"&"pting.File"&"Sys"&"temObject")
	  If not fs.FolderExists(lpath) then
		  Set f=fs.CreateFolder(lpath)
		  CreateFolder=F.Path
		end if
	  Set F=Nothing
	  Set fs=Nothing
	End Function
%>