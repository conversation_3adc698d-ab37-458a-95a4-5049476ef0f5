/**
 * Licensed under the GPL licenses
 */
(function(e){function a(h){var j=e.extend(true,{},e.fn.combobox.defaults,e(h).combobox("options"));var g=j.customAttr.slave;if(g.id==null){return}if(/^#/.test(g.id)){g.id=g.id}else{g.id="#"+g.id}if(!j.multiple&&!j.editable){e(h).combobox("addEventListener",[{name:"onSelect",handler:function(k){b(h,g,k,j.valueField)}},{name:"onChange",handler:function(l,k){if(l==null||l==""){e(g.id).combobox("clear").combobox("loadData",[]);e(h).combobox("textbox").trigger("blur")}}}])}}function b(k,l,g,j){if(l.remote){var h=l.url||e(l.id).combobox("options").url;if(h.indexOf("?")>-1){h+="&swd="+g[j]}else{h+="?swd="+g[j]}e(l.id).combobox("clear").combobox("reload",h)}else{e(l.id).combobox("clear").combobox("loadData",g.data)}}function f(h){var j=e(h).combobox("options");var g=e.extend(true,{},e.fn.combobox.defaults,j);if(!g.customAttr.headervalue){return}e(h).combobox("setText",g.customAttr.headervalue).combobox("addEventListener",{name:"onLoadSuccess",handler:function(){e(h).combobox("textbox").trigger("blur")}})}function d(l,g,k,j){var h=e(l).combobox("options");var m=h[g];switch(g){case"onBeforeLoad":if(j){h[g]=k}else{h[g]=function(n){m.apply(this,arguments);k.apply(this,arguments)}}break;case"onLoadSuccess":if(j){h[g]=k}else{h[g]=function(){m.apply(this,arguments);k.apply(this,arguments)}}break;case"onLoadError":if(j){h[g]=k}else{h[g]=function(){m.apply(this,arguments);k.apply(this,arguments)}}break;case"onSelect":if(j){h[g]=k}else{h[g]=function(n){m.apply(this,arguments);k.apply(this,arguments)}}break;case"onUnselect":if(j){h[g]=k}else{h[g]=function(n){m.apply(this,arguments);k.apply(this,arguments)}}break;default:e(l).combo("addEventListener",{name:g,override:j,handler:k});break}}e.fn.combobox.defaults.customAttr={slave:{id:null,remote:true,url:null}};e.extend(e.fn.combobox.methods,{followCustomHandle:function(g){return g.each(function(){})},addEventListener:function(h,g){return h.each(function(){var j=e.isArray(g)?g:[g];var k=this;e.each(j,function(l,m){d(k,m.name,m.handler||function(){},m.override)})})},getSelected:function(n){var g=n.combobox("options");var j=g.valueField;var m=n.combobox("getValue");var l=n.combobox("getData");for(var h=0;h<l.length;h++){var k=l[h];if(k[j]==m){return k}}return null}});var c=e.fn.combobox;e.fn.combobox=function(g,h){if(typeof g!="string"){return this.each(function(){c.call(e(this),g,h);f(this);a(this)})}else{return c.call(this,g,h)}};e.fn.combobox.methods=c.methods;e.fn.combobox.defaults=c.defaults;e.fn.combobox.parseOptions=c.parseOptions;e.fn.combobox.parseData=c.parseData})(jQuery);(function(g){function c(k){var l=g.data(k,"combo").options;var j=g.extend(true,{},g.fn.combo.defaults,l);if(!j.customAttr.headervalue){return}if(l.required){var h=['unequal["'+j.customAttr.headervalue+'"]'];if(l.validType){if(typeof l.validType=="string"){h.push(l.validType);l.validType=h}if(g.isArray(l.validType)){g.merge(l.validType,h)}}else{g.extend(l,{validType:h})}}g(k).combo("addEventListener",{name:"onChange",handler:function(n,m){if(n==null||n==""){g(k).combo("setText",j.customAttr.headervalue)}}}).combo("textbox").val(j.customAttr.headervalue).attr("prompt",j.customAttr.headervalue).focus(function(){if(g(this).val()==j.customAttr.headervalue){g(this).val("")}}).blur(function(){if(g.trim(g(this).val())==""){g(this).val(j.customAttr.headervalue)}g(k).combo("validate")})}function a(k){var j=g(k).combo("getValue");if(!j){return}var h=g.data(k,"combo").options;g(k).combo("setText","");if(h.multiple){g(k).combo("setValues",[])}else{g(k).combo("setValue","")}}function b(j){var h=g(j).combo("getValues");return h.length>0?(h[0]!=""?h[0]:null):null}function f(j){var k=g.extend(true,{},g.fn.combo.defaults,g.data(j,"combo").options);var h=k.customAttr.autocomplete;if(!h.enabled){return}g(j).combo("textbox").keyup(function(l){if(g(this).val().length!=0&&(g(this).val().length%h.minLength==0)&&h.url){g.ajax({type:"POST",url:h.url,data:{wd:g(this).val()},dataType:"json",success:function(o){var m=g(j).combo("panel").empty();for(var n=0;n<o.length;n++){g("<div>").addClass("combobox-item").attr("value",o[n].id).text(o[n].text).click(function(r){var p=g(this).attr("value");var q=g(this).text();g(j).combo("setValue",p).combo("setText",q).combo("hidePanel")}).hover(function(){g(this).addClass("combobox-item-hover")},function(){g(this).removeClass("combobox-item-hover")}).appendTo(m)}},error:function(m,o,n){g.messager.alert("Error",n,"error")}})}})}function e(m,h,l,k){var j=g(m).combo("options");var n=j[h];switch(h){case"onShowPanel":if(k){j[h]=l}else{j[h]=function(){n.apply(this,arguments);l.apply(this,arguments)}}break;case"onHidePanel":if(k){j[h]=l}else{j[h]=function(){n.apply(this,arguments);l.apply(this,arguments)}}break;case"onChange":if(k){j[h]=l}else{j[h]=function(p,o){n.apply(this,arguments);l.apply(this,arguments)}}break;default:break}}g.fn.combo.defaults.customAttr={headervalue:null,autocomplete:{enabled:false,minLength:3,url:undefined}};g.extend(g.fn.combo.methods,{followCustomHandle:function(h){return h.each(function(){})},clear:function(h){return h.each(function(){a(this)})},getValue:function(h){return b(h[0])},addEventListener:function(j,h){return j.each(function(){var k=g.isArray(h)?h:[h];var l=this;g.each(k,function(m,n){e(l,n.name,n.handler||function(){},n.override)})})}});var d=g.fn.combo;g.fn.combo=function(h,j){if(typeof h!="string"){return this.each(function(){d.call(g(this),h,j);c(this);f(this)})}else{return d.call(this,h,j)}};g.fn.combo.methods=d.methods;g.fn.combo.defaults=d.defaults;g.fn.combo.parseOptions=d.parseOptions})(jQuery);(function(a){a.extend(a.fn.combogrid.methods,{getSelected:function(b){return b.combogrid("grid").datagrid("getSelected")}})})(jQuery);(function(a){a.extend(a.fn.combotree.methods,{followCustomHandle:function(b){return b.each(function(){})}})})(jQuery);(function(d){function w(B,C,y){var A=j(B,y);var z=d("#"+A);if(z.length==0){z=d("<div>",{id:A}).menu().menu("appendItems",C)}return z}function j(z,y){return d(z).attr("id")+"_"+y}function u(z){var y={};d.each(z,function(){var B=this;if(B.onclick){var A=B.id||B.text;y[A]=B.onclick;delete B.onclick}if(B.submenu&&d.isArray(B.submenu)&&B.submenu.length>0){d.extend(y,u(B.submenu))}});return y}function o(F){var E=j(F,"headerContextMenu");var z=[{text:"显示/隐藏列",iconCls:"icon-columns",submenu:[{id:E+"_showAll",text:"全部显示",iconCls:"icon-columns",onclick:function(H,I,G){d.fn.datagrid.headerContextMenu.defaultEvents.doShowAll(G)}},{id:E+"_restore",text:"还原",iconCls:"icon-columns",onclick:function(H,I,G){d.fn.datagrid.headerContextMenu.defaultEvents.doRestore(G)}},"-"]}];var B=function(G){return G.substr(G.lastIndexOf("_")+1,G.length)};var C=[];var A=d(F).datagrid("getColumnFields",true);var D=d(F).datagrid("getColumnFields");y(D,false);function y(H,G){d.each(H,function(J,K){if(!K||K=="ck"){return true}var I=d(F).datagrid("getColumnOption",K);I._hidden=I.hidden;C.push({id:E+"_"+K,text:I.title,disabled:G,iconCls:I.hidden?"icon-unchecked":"icon-checked",onclick:function(M,L,P){var O=B(M.id);var N=d(P).datagrid("getColumnOption",O).hidden;if(!N){d.fn.datagrid.headerContextMenu.defaultEvents.doHideColumn(P,O,M)}else{d.fn.datagrid.headerContextMenu.defaultEvents.doShowColumn(P,O,M)}}})})}d.merge(z[0].submenu,C);return z}function n(B){var z=d.extend(true,{},d.fn.datagrid.defaults,d(B).datagrid("options"));var D=z.customAttr.headerContextMenu;if(!D.isShow){return}if(z.columns[0][0].checkbox){z.columns[0][0].field="ck"}var C=o(B);if(D.isMerge){d.merge(C,D.items)}if(!D.isMerge&&d.isArray(D.items)&&D.items.length>0){C=D.items}var A=u(C);var y=w(B,C,"headerContextMenu");d(B).datagrid("addEventListener",{name:"onHeaderContextMenu",handler:function(F,E){F.preventDefault();y.menu("addEventListener",[{name:"onClick",override:true,handler:function(H){var G=H.id||H.text;if(A[G]){A[G].call(this,H,E,B)}}},{name:"onShow",override:true,handler:function(){D.onShow&&D.onShow.call(this,E,B)}},{name:"onHide",override:true,handler:function(){D.onHide&&D.onHide.call(this)}}]).menu("show",{left:F.pageX,top:F.pageY})}})}function k(A){var z=j(A,"rowContextMenu");var y=[{id:z+"_delete",text:"删除",iconCls:"icon-remove",onclick:function(C,E,D,B){d.fn.datagrid.rowContextMenu.defaultEvents.doDelete(C,E,D,B)}},"-",{id:z+"_reload",text:"刷新",iconCls:"icon-reload",onclick:function(C,E,D,B){d.fn.datagrid.rowContextMenu.defaultEvents.doReload(C,E,D,B)}},{id:z+"_reload_this_page",text:"刷新当前页",onclick:function(C,E,D,B){d.fn.datagrid.rowContextMenu.defaultEvents.doReloadThisPage(C,E,D,B)}}];return y}function f(C){var z=d.extend(true,{},d.fn.datagrid.defaults,d(C).datagrid("options"));var A=z.customAttr.rowContextMenu;if(!A.isShow){return}var D=k(C);if(A.isMerge){d.merge(D,A.items)}if(!A.isMerge&&d.isArray(A.items)&&A.items.length>0){D=A.items}var B=u(D);var y=w(C,D,"rowContextMenu");d(C).datagrid("addEventListener",{name:"onRowContextMenu",handler:function(F,G,E){F.preventDefault();d(C).datagrid("selectRow",G);y.menu("addEventListener",{name:"onClick",override:true,handler:function(I){var H=I.id||I.text;if(B[H]){B[H].call(this,I,G,E,C)}}}).menu("show",{left:F.pageX,top:F.pageY})}})}function g(E){var G=d.extend(true,{},d.fn.datagrid.defaults,d(E).datagrid("options"));if(!d.isArray(G.customAttr.slaveList)){return}if(G.customAttr.slaveList.length==0){return}var F={slaveList:G.customAttr.slaveList,activeSlave:G.customAttr.activeSlave};var A=d(E);var z=[];for(var D in F.slaveList){var B={id:F.slaveList[D].id,params:{}};var C={},y;if(!F.slaveList[D].relatedfield){y=A.datagrid("options").idField;C[y]="undefined"}else{y=F.slaveList[D].relatedfield;C[F.slaveList[D].relatedfield]="undefined"}d.extend(B.params,C,F.slaveList[D].queryParams);z.push(B)}if(!F.activeSlave||d.trim(F.activeSlave).length==0){F.activeSlave="onDblClickRow"}A.datagrid("addEventListener",{name:F.activeSlave,handler:function(J,I){for(var H in z){z[H].params[y]=I[y];d("#"+z[H].id).datagrid("load",z[H].params)}}})}function r(D){var z=d.extend(true,{},d.fn.datagrid.defaults,d(D).datagrid("options"));if(!z.customAttr.rowediting){return}var E=function(F){return d(F).attr("id")+"_editor_buttons_panel"};var y=120;var B=function(K){var I=E(K);if(d("#"+I).length>0){return}var G=d(K).datagrid("getPanel");var J=d.data(K,"datagrid");var H=J.dc.body2;H.css("position","relative");var F=d("<div>",{id:I}).addClass("dialog-button").appendTo(H).css({position:"absolute",display:"block","border-bottom":"1px solid #ddd","border-left":"1px solid #ddd","border-right":"1px solid #ddd",left:parseInt(G.width()/2)-y,"z-index":2013,display:"none",padding:"4px 5px"});d('<a href="javascript:void(0)">确定</a>').css("margin-left","0px").linkbutton({iconCls:"icon-ok"}).click(function(){var L=d(K).datagrid("getRowIndex",d(K).datagrid("getEditingRow"));if(!z.customAttr.onConfirmEdit.call(K,L)){return}d(K).datagrid("endEdit",L)}).appendTo(F);d('<a href="javascript:void(0)">取消</a>').css("margin-left","6px").linkbutton({iconCls:"icon-cancel"}).click(function(){var L=d(K).datagrid("getRowIndex",d(K).datagrid("getEditingRow"));d(K).datagrid("cancelEdit",L)}).appendTo(F)};var C=function(M,L){var F=d.data(M,"datagrid").options;var N=F.finder.getTr(M,L,"body",2);var I=N.position();var H="#"+E(M);var G=d.data(M,"datagrid");var J=G.dc.body2;var K=function(){var O=N.height(),P=N.width();var R=I.top+J.scrollTop(),Q=I.left;var S=11;if(P>J.width()){Q=J.width()/2-y}else{Q=P/2-y}if(I.top+(O*2+S)>J.height()){R=R-(O+S)}else{R=R+O}return{top:R,left:Q}};d(H).css(K()).show()};var A=function(G){var F="#"+E(G);d(F).hide()};d(D).datagrid("addEventListener",[{name:"onLoadSuccess",handler:function(F){B(this)}},{name:"onBeforeEdit",handler:function(F,G){B(D);C(D,F)}},{name:"onAfterEdit",handler:function(F,H,G){A(D)}},{name:"onCancelEdit",handler:function(F,G){A(D)}}])}function v(A){var z=d.extend(true,{},d.fn.datagrid.defaults,d(A).datagrid("options"));if(!z.customAttr.tooltip.enable){return}var D=function(G,F){var E={position:z.customAttr.tooltip.position,trackMouse:true,onHide:function(){d(G).tooltip("destroy")},onShow:function(){if(d.isPlainObject(F)&&F.css){d(this).tooltip("tip").css(F.css)}}};d.extend(E,d.isPlainObject(F)?F:{content:F});d(G).tooltip(E).tooltip("show")};var C=function(I,F){var J=parseInt(I.attr("datagrid-row-index"));var H=d(A).datagrid("getRows")[J];var E=function(M){var L=[];var K=d.grep(d.merge(d(A).datagrid("getColumnFields",true),d(A).datagrid("getColumnFields")),function(O,N){return d.trim(O).length>0});d.each(K,function(){var N=this;var O=d(A).datagrid("getColumnOption",N).title;L.push(O+": "+M[N])});return L.join("<br>")};var G=F?F(H,J):E(H);I.mouseover(function(){D(this,G)})};var y=function(E,F){E.mouseover(function(){var K=d(this).parent().attr("datagrid-row-index");var I=d(A).datagrid("getRows")[K];var J=d(this).attr("field");var H=I[J];var G=F?F(H,J):H;D(this,G)})};var B=function(){if(z.customAttr.tooltip.target=="row"){z.finder.getTr(A,"","allbody").each(function(){var H=d(this);if(H.hasClass("datagrid-row")){C(H,z.customAttr.tooltip.formatter)}})}else{if(z.customAttr.tooltip.fields&&d.isArray(z.customAttr.tooltip.fields)){var E=d(A).datagrid("getPanel");var G=d.data(A,"datagrid");var F=G.dc.body2;d.each(z.customAttr.tooltip.fields,function(){var H=this;y(d("td[field="+H+"]",F),z.customAttr.tooltip.formatter)})}}};d(A).datagrid("addEventListener",{name:"onLoadSuccess",handler:function(E){B()}})}function b(z){var y=d.extend(true,{},d.fn.datagrid.defaults,d(z).datagrid("options"));if(!y.pagination){return}d(z).datagrid("addEventListener",{name:"onLoadSuccess",handler:function(A){d(z).datagrid("setPagination",y.customAttr.pagination)}})}function s(A){var z=d(A).datagrid("options");var y=function(D){var C=d(A).datagrid("getPanel");if(D.rows.length==0){var E=d("div.datagrid-view2>div.datagrid-header>div.datagrid-header-inner>table",C)[0];var B=d(">div.datagrid-view>div.datagrid-view2>div.datagrid-body",C);d("<div>").html("&nbsp;").width(d(E).width()).appendTo(B)}else{d("div.datagrid-view2>div.datagrid-body>div",C).remove()}};d(A).datagrid("addEventListener",[{name:"onLoadSuccess",handler:function(B){y(B)}},{name:"onLoadError",handler:function(){y({rows:[]})}}])}function q(B,A){var y=d(B).datagrid("options");var C=d(B).datagrid("getColumnFields",true);var z=y.columns[0][0];if(C.length==0&&z.checkbox){x(B,"ck",2,1)}t(B,A,true);x(B,A,2,1)}function p(D,C){var y=d(D).datagrid("options");if(!y.frozenColumns[0]){y.frozenColumns=[[]]}var B=d(D).datagrid("getColumnOption",C);y.frozenColumns[0].push(B);A(B);d(D).datagrid(y);var z=d(D).datagrid("getHeaderContextMenu").menu("findItem",B.title);d(D).datagrid("getHeaderContextMenu").menu("disableItem",z.target);function A(G){for(var F=0;F<y.columns.length;F++){for(var E=0;E<y.columns[F].length;E++){if(y.columns[F][E].field==G.field){y.columns[F].splice(E,1);return}}}}}function x(C,D,E,F){var H=d(C).datagrid("options");var G=d.data(C,"datagrid").dc;var y=null;var y=(E==1?G.header1:G.header2).find(">table>tbody>tr.datagrid-header-row>td[field="+D+"]");if(E>F){(F==1?G.header1:G.header2).find(">table>tbody>tr.datagrid-header-row").append(y)}else{(F==1?G.header1:G.header2).find(">table>tbody>tr.datagrid-header-row").children("td[field]").each(function(){if(B(D,d(this).attr("field"))){d(this).before(y);return false}})}var z=(E==1?G.body1:G.body2).find(">table>tbody>tr>td[field="+D+"]");if(E>F){d.each(z,function(I,J){H.finder.getTr(C,I,"body",F).append(J)})}else{d.each(z,function(I,J){H.finder.getTr(C,I,"body",F).children("td[field]").each(function(){if(B(D,d(this).attr("field"))){d(this).before(J);return false}})})}d(C).datagrid("fixColumnSize");function B(I,J){return A(I)<A(J)}function A(I){return d.inArray(I,d(C).datagrid("getColumnFields"))}}function t(E,D,A){var z=F(D);var C=d(E).datagrid("getColumnOption",D);d.extend(C,{index:z});var y=d(E).datagrid("getHeaderContextMenu");var B=y.menu("findItem",C.title);if(!B){return}if(A){y.menu("disableItem",B.target)}else{y.menu("enableItem",B.target)}function F(G){return d.inArray(G,d(E).datagrid("getColumnFields"))}}function h(E,D){var A=d(D).datagrid("getHeaderContextMenu");var C=d(D).datagrid("getColumnOption",E);var z=A.menu("findItem",C.title);if(z){if(!z.disabled){y("冻结此列");B("取消冻结")}else{y("取消冻结");B("冻结此列")}}else{B("冻结此列");B("取消冻结")}function B(G){var F=A.menu("findItem",G);if(F){A.menu("disableItem",F.target)}}function y(G){var F=A.menu("findItem",G);if(F){A.menu("enableItem",F.target)}}}function e(A,z,y){if(y){q(A,z)}else{p(A,z)}}function a(z,y){t(z,y,false);x(z,y,1,2)}function l(C,y,B,A){var z=d(C).datagrid("options");var D=z[y];switch(y){case"onLoadSuccess":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onLoadError":if(A){z[y]=B}else{z[y]=function(){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onBeforeLoad":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onClickRow":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onDblClickRow":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onClickCell":if(A){z[y]=B}else{z[y]=function(G,F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onDblClickCell":if(A){z[y]=B}else{z[y]=function(G,F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onSortColumn":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onResizeColumn":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onSelect":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onUnselect":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onSelectAll":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onUnselectAll":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);handlerapply(this,arguments)}}break;case"onCheck":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onUncheck":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onCheckAll":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onUncheckAll":if(A){z[y]=B}else{z[y]=function(E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onBeforeEdit":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onAfterEdit":if(A){z[y]=B}else{z[y]=function(G,F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onBeginEdit":if(A){z[y]=B}else{z[y]=function(G,F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onEndEdit":if(A){z[y]=B}else{z[y]=function(G,F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onCancelEdit":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onHeaderContextMenu":if(A){z[y]=B}else{z[y]=function(F,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onRowContextMenu":if(A){z[y]=B}else{z[y]=function(F,G,E){D.apply(this,arguments);B.apply(this,arguments)}}break;case"onExpandRow":if(A){z[y]=B}else{z[y]=function(E,F){D&&D.apply(this,arguments);B.apply(this,arguments)}}break;case"onCollapseRow":if(A){z[y]=B}else{z[y]=function(E,F){D&&D.apply(this,arguments);B.apply(this,arguments)}}break;default:break}}function m(A){var z=d(A).datagrid("options");var y=[];z.finder.getTr(A,"","allbody",1).each(function(){if(d("span.datagrid-row-collapse",this).length>0){y.push(d(this).attr("datagrid-row-index"))}});return y}d.fn.datagrid.headerContextMenu={defaultEvents:{doHideColumn:function(A,z,y){d(A).datagrid("hideColumn",z);var B=d(A).datagrid("getHeaderContextMenu");B.menu("setIcon",{target:y.target,iconCls:"icon-unchecked"})},doShowColumn:function(A,z,y){d(A).datagrid("showColumn",z);var B=d(A).datagrid("getHeaderContextMenu");B.menu("setIcon",{target:y.target,iconCls:"icon-checked"})},doShowAll:function(B){var y=d(B).datagrid("getColumnFields");var C=d(B).datagrid("getHeaderContextMenu");for(i in y){d(B).datagrid("showColumn",y[i]);var z=d(B).datagrid("getColumnOption",y[i]);var A=C.menu("findItem",z.title);if(A){C.menu("setIcon",{target:A.target,iconCls:"icon-checked"})}}},doRestore:function(B){var y=d(B).datagrid("getColumnFields");var C=d(B).datagrid("getHeaderContextMenu");for(i in y){var z=d(B).datagrid("getColumnOption",y[i]);var A=C.menu("findItem",z.title);if(!z._hidden){d(B).datagrid("showColumn",y[i]);A&&C.menu("setIcon",{target:A.target,iconCls:"icon-checked"})}else{d(B).datagrid("hideColumn",y[i]);A&&C.menu("setIcon",{target:A.target,iconCls:"icon-unchecked"})}}}}};d.fn.datagrid.rowContextMenu={defaultEvents:{doAdd:function(y,B,z,A){},doEdit:function(y,B,z,A){},doDelete:function(y,B,z,A){d.messager.confirm("疑问","您确定要删除已选中的行？",function(C){if(C){d(A).datagrid("deleteRows",d(A).datagrid("getSelections"))}})},doReload:function(y,B,z,A){d(A).datagrid("load")},doReloadThisPage:function(y,B,z,A){d(A).datagrid("reload")},doExportThisPage:function(y,B,z,A){},doExprotAll:function(y,B,z,A){}}};d.extend(d.fn.datagrid.defaults.editors,{my97:{init:function(y,A){var z=d('<input type="text" class="Wdate">').appendTo(y);A=A||{};A=d.extend({},A,{readOnly:true});return z.focus(function(){WdatePicker()})},getValue:function(y){return d(y).val()},setValue:function(z,y){d(z).val(y)},resize:function(A,z){var y=d(A);if(d.boxModel==true){y.width(z-(y.outerWidth()-y.width()))}else{y.width(z)}}},datetimebox:{init:function(y,A){var z=d('<input type="text" class="easyui-datetimebox">').appendTo(y);A=A||{};A=d.extend({},A,{formatter:function(B){return d.dateFormat(new Date(B),"yyyy-MM-dd hh:mm")}});return z.datetimebox(A)},getValue:function(y){return d(y).datetimebox("getValue")},setValue:function(z,y){d(z).datetimebox("setValue",y)},resize:function(z,y){d(z).datetimebox("resize",y)}},numberspinner:{init:function(y,A){var z=d('<input type="text">').appendTo(y);A=A||{};A=d.extend({},A,{min:0,editable:false});return z.numberspinner(A)},getValue:function(y){return d(y).numberspinner("getValue")},setValue:function(z,y){d(z).numberspinner("setValue",y)},resize:function(z,y){d(z).numberspinner("resize",y)}},timespinner:{init:function(y,A){var z=d('<input type="text">').appendTo(y);A=A||{};return z.timespinner(A)},getValue:function(y){return d(y).timespinner("getValue")},setValue:function(z,y){d(z).timespinner("setValue",y)},resize:function(z,y){d(z).timespinner("resize",y)}},combogrid:{init:function(y,A){var z=d('<input type="text">').appendTo(y);A=A||{};A=d.extend({},{panelWidth:400,editable:false},A);return z.combogrid(A)},getValue:function(y){return d(y).combogrid("getValue")},setValue:function(z,y){d(z).combogrid("setValue",y)},resize:function(z,y){d(z).combogrid("resize",y)}}});d.fn.datagrid.defaults.customAttr={headerContextMenu:{isShow:false,isMerge:true,items:[],onShow:function(z,y){},onHide:function(){}},rowContextMenu:{isShow:false,isMerge:true,items:[]},pagination:{showPageList:true,showRefresh:true,beforePageText:undefined,afterPageText:undefined,displayMsg:undefined},slaveList:undefined,activeSlave:"onDblClickRow",rowediting:false,tooltip:{enable:false,target:"row",position:"bottom",fields:undefined,formatter:undefined},onConfirmEdit:function(y){return true}};d.extend(d.fn.datagrid.methods,{followCustomHandle:function(y){return y.each(function(){})},getHeaderContextMenu:function(y){return d("#"+j(y[0],"headerContextMenu"))},getRowContextMenu:function(y){return d("#"+j(y[0],"rowContextMenu"))},getEditingRow:function(z){var y=z.datagrid("getEditingRows");return y.length?y[0]:null},getEditingRows:function(C){var y=d.data(C[0],"datagrid");var A=y.options;var B=y.data;var z=[];A.finder.getTr(C[0],"","allbody").each(function(){if(d(this).hasClass("datagrid-row-editing")){var D=parseInt(d(this).attr("datagrid-row-index"));z.push(B.rows[D])}});return z},setPagination:function(z,y){return z.each(function(){d(this).datagrid("getPager").pagination(y)})},deleteRows:function(z,y){return z.each(function(){var B=undefined;if(!d.isArray(y)){B=[y]}else{B=y}var A=this;d.each(B,function(C,D){setTimeout(function(){var E=d(A).datagrid("getRowIndex",D);d(A).datagrid("deleteRow",E)},5)})})},freezeColumn:function(z,y){return z.each(function(){e(this,y,true)})},unfreezColumn:function(z,y){return z.each(function(){a(this,y)})},addEventListener:function(z,y){return z.each(function(){var A=d.isArray(y)?y:[y];var B=this;d.each(A,function(C,D){l(B,D.name,D.handler||function(){},D.override)})})},fixDetailRowWidth:function(z,y){return z.each(function(){var B=d.data(this,"datagrid");var A=B.dc.header2.children();y.handler&&y.handler.call(this,y.index,A.width())})},getAllExpandRowIndex:function(y){return m(y[0])},getExpandRowIndex:function(z){var y=z.datagrid("getAllExpandRowIndex");return y.length>0?y[0]:-1}});var c=d.fn.datagrid;d.fn.datagrid=function(y,z){if(typeof y!="string"){return this.each(function(){c.call(d(this),y,z);s(this);n(this);f(this);b(this);g(this);r(this);v(this)})}else{return c.call(this,y,z)}};d.fn.datagrid.methods=c.methods;d.fn.datagrid.defaults=c.defaults;d.fn.datagrid.parseOptions=c.parseOptions;d.fn.datagrid.parseData=c.parseData;d.fn.datagrid.rowContextMenu=c.rowContextMenu;d.fn.datagrid.headerContextMenu=c.headerContextMenu})(jQuery);(function(a){a.extend({dateFormat:function(c,k){if(c==null){return null}if(k==null){var p="yyyy-MM-dd"}else{var p=k}var l=c.getFullYear().toString();var j=(c.getMonth()+1).toString();var m=c.getDate().toString();var n=c.getHours().toString();var e=c.getMinutes().toString();var o=c.getSeconds().toString();var d=p.replace(/[^y]/g,"");var q=p.replace(/[^M]/g,"");var b=p.replace(/[^d]/g,"");var g=p.replace(/[^h]/g,"");var f=p.replace(/[^m]/g,"");var h=p.replace(/[^s]/g,"");if(d.length==2){l=l.substring(2,4)}if(q.length>1&&j.length==1){j="0"+j}if(b.length>1&&m.length==1){m="0"+m}if(g.length>1&&n.length==1){n="0"+n}if(f.length>1&&e.length==1){e="0"+e}if(h.length>1&&o.length==1){o="0"+o}if(d.length>0){p=p.replace(d,l)}if(q.length>0){p=p.replace(q,j)}if(b.length>0){p=p.replace(b,m)}if(g.length>0){p=p.replace(g,n)}if(f.length>0){p=p.replace(f,e)}if(h.length>0){p=p.replace(h,o)}return p},parseDate:function(u,p){var t=new Date();if(u==null){return t}if(p==null){var m="yyyy-MM-dd"}else{var m=p}var w=m.replace(/[^y]/g,"");var l=m.replace(/[^M]/g,"");var h=m.replace(/[^d]/g,"");var x=m.replace(/[^h]/g,"");var d=m.replace(/[^m]/g,"");var f=m.replace(/[^s]/g,"");var j=m.indexOf(w);var q=w.length;var e=parseInt(u.substring(j,j+q));if(isNaN(e)){e=t.getYear()}else{if(q==2){if(e<50){e+=2000}else{e+=1900}}}var y=m.indexOf(l);var v=parseInt(u.substring(y,y+l.length));if(isNaN(v)){v=t.getMonth()}else{v-=1}var b=m.indexOf(h);var n=parseInt(u.substring(b,b+h.length));if(isNaN(n)){n=t.getDate()}var s=m.indexOf(x);var o=parseInt(u.substring(s,s+x.length));if(isNaN(o)){o=0}var c=m.indexOf(d);var k=parseInt(u.substring(c,c+d.length));if(isNaN(k)){k=0}var r=m.indexOf(f);var g=parseInt(u.substring(r,r+f.length));if(isNaN(g)){g=0}return new Date(e,v,n,o,k,g)}})})(jQuery);(function(b){function a(d,c){if(b("#"+d).length>0){return}return b("<style>"+c+"</style>").attr("id",d).attr("type","text/css").appendTo("head")}b.extend({mask:function(f){f=f||{};var d=b.extend({},{target:"body",loadMsg:b.fn.datagrid.defaults.loadMsg},f);this.unmask(d);if(d.target!="body"&&b(d.target).css("position")=="static"){b(d.target).addClass("mask-relative")}var c=b('<div class="datagrid-mask" style="display:block;"></div>').appendTo(d.target);var g=b('<div class="datagrid-mask-msg" style="display:block; left: 50%;"></div>').html(d.loadMsg).appendTo(d.target);setTimeout(function(){g.css("marginLeft",-g.outerWidth()/2)},5);var e=".mask-relative {position: relative !important;}";a("mask_css",e)},unmask:function(c){var d=c.target||"body";b(">div.datagrid-mask-msg",d).remove();b(">div.datagrid-mask",d).remove();b(c.target).removeClass("mask-relative")}})})(jQuery);(function(c){function a(f,e,d){if(e&&c.isArray(e)){c.each(e,function(){var j=this;var h={};if(d){c.extend(h,{parent:d})}if(c.isPlainObject(j)){c(f).menu("appendItem",c.extend(j,h));if(j.submenu){var k=c(f).menu("findItem",j.text);a(f,j.submenu,k.target)}}else{if(j=="-"){var g=c(f).menu("appendItem",c.extend({text:j},h)).menu("findItem",j).target;c(g).removeClass("menu-item").addClass("menu-sep").removeAttr("style").empty()}}})}}function b(h,d,g,f){var e=c(h).menu("options");var j=e[d];switch(d){case"onShow":if(f){e[d]=g}else{e[d]=function(){j.apply(this,arguments);g.apply(this,arguments)}}break;case"onHide":if(f){e[d]=g}else{e[d]=function(){j.apply(this,arguments);g.apply(this,arguments)}}break;case"onClick":if(f){e[d]=g}else{e[d]=function(k){j.apply(this,arguments);g.apply(this,arguments)}}break;default:break}}c.extend(c.fn.menu.methods,{followCustomHandle:function(d){return this.each(function(){})},appendItems:function(e,d){return e.each(function(){a(this,d)})},addEventListener:function(e,d){return e.each(function(){var f=c.isArray(d)?d:[d];var g=this;c.each(f,function(h,j){b(g,j.name,j.handler||function(){},j.override)})})}})})(jQuery);(function(c){function d(j){var g=c.extend(true,{},c.fn.panel.defaults,c(j).panel("options"));var h=g.customAttr.toolbar;if(!h){return}var e=c(j).panel("body");if(typeof h=="string"){c(h).addClass("dialog-toolbar panel-body").insertBefore(e);c(h).show()}else{if(c.isArray(h.data)&&h.data.length>0){var f=c("<div></div>").insertBefore(e);f.toolbar(h)}}}function b(j,e,h,g){var f=c(j).panel("options");var k=f[e];switch(e){case"onResize":if(g){f[e]=h}else{f[e]=function(m,l){k.apply(this,arguments);h.apply(this,arguments)}}break;case"onMove":if(g){f[e]=h}else{f[e]=function(m,l){k.apply(this,arguments);h.apply(this,arguments)}}break;default:if(g){f[e]=h}else{f[e]=function(){k.apply(this,arguments);h.apply(this,arguments)}}}}c.fn.panel.defaults.customAttr={toolbar:{buttonPosition:"left",data:[]}};c.extend(c.fn.panel.methods,{followCustomHandle:function(e){return e.each(function(){})},addEventListener:function(f,e){return f.each(function(){var g=c.isArray(e)?e:[e];var h=this;c.each(g,function(j,k){b(h,k.name,k.handler||function(){},k.override)})})}});var a=c.fn.panel;c.fn.panel=function(e,f){if(typeof e!="string"){return this.each(function(){a.call(c(this),e,f);d(this)})}else{return a.call(this,e,f)}};c.fn.panel.methods=a.methods;c.fn.panel.defaults=a.defaults;c.fn.panel.parseOptions=a.parseOptions})(jQuery);(function(b){var c=function(e,j){var f=e;var o=j;if(f<1){f=1}if(o<1){o=1}var d=parseInt(b(this).parent().css("width"))+14;var m=parseInt(b(this).parent().css("height"))+14;var k=f+d;var h=o+m;var n=b(window).width();var g=b(window).height();if(k>n){f=n-d}if(h>g){o=g-m}b(this).parent().css({left:f,top:o})};b.fn.dialog.defaults.onMove=c;b.fn.window.defaults.onMove=c;b.fn.panel.defaults.onMove=c;var a=function(){var d=b("iframe",this);if(d.length>0){d[0].contentWindow.close();d.remove();if(navigator.userAgent.indexOf("MSIE")>0){CollectGarbage()}}};b.fn.panel.defaults.onBeforeDestroy=a;b.fn.window.defaults.onBeforeDestroy=a;b.fn.dialog.defaults.onBeforeDestroy=a})(jQuery);(function(f){function g(p){return f(p).attr("id")+"_contextmenu"}function j(r,s){var q=g(r);var p=f("#"+q);if(p.length==0){p=f("<div>",{id:q}).menu();p.menu("appendItems",s)}return p}function n(q){var p={};f.each(q,function(){var s=this;if(s.onclick){var r=s.id||s.text;p[r]=s.onclick;delete s.onclick}if(s.submenu&&f.isArray(s.submenu)&&s.submenu.length>0){f.extend(p,n(s.submenu))}});return p}function o(t){var q=f.extend(true,{},f.fn.tabs.defaults,f.data(t,"tabs").options);var p=q.customAttr.contextMenu;if(!p.isShow){return}var u=m(t);if(p.isMerge&&f.isArray(p.items)&&p.items.length>0){u=f.merge(u,p.items)}if(!p.isMerge&&f.isArray(p.items)&&p.items.length>0){u=p.items}var s=n(u);var r=j(t,u);f(t).tabs("addEventListener",{name:"onContextMenu",handler:function(w,x,v){w.preventDefault();k(t,r,v);r.menu("addEventListener",{name:"onClick",override:true,handler:function(z){var y=z.id||z.text;if(s[y]){s[y].call(this,z,x,v,t)}}}).menu("show",{left:w.pageX,top:w.pageY})}})}function k(t,r,p){var s=g(t);var q=f("#"+s+"_fixed");if(f.inArray(p,f.fn.tabs.defaults.customAttr.fixedtabs)==-1&&!f(t).tabs("getTab",p).panel("options").closable){r.menu("setText",{target:q,text:f.fn.tabs.defaults.contextMenu.itemname.unfixtab})}else{r.menu("setText",{target:q,text:f.fn.tabs.defaults.contextMenu.itemname.fixtab});if(f.inArray(p,f.fn.tabs.defaults.customAttr.fixedtabs)>-1){r.menu("disableItem",q)}else{r.menu("enableItem",q)}}q=f("#"+s+"_close");if(!f(t).tabs("getTab",p).panel("options").closable){r.menu("disableItem",q)}else{r.menu("enableItem",q)}}function m(q){var p=g(q);return[{id:p+"_reload",text:f.fn.tabs.defaults.contextMenu.itemname.reload,onclick:f.fn.tabs.defaults.contextMenu.defaultEventsHandler.reload},{id:p+"_fixed",text:f.fn.tabs.defaults.contextMenu.itemname.fixtab,onclick:function(t,u,r,s){if(t.text==f.fn.tabs.defaults.contextMenu.itemname.fixtab){f.fn.tabs.defaults.contextMenu.defaultEventsHandler.fixtab(t,u,r,s)}else{f.fn.tabs.defaults.contextMenu.defaultEventsHandler.unfixtab(t,u,r,s)}}},"-",{id:p+"_close",text:f.fn.tabs.defaults.contextMenu.itemname.close,onclick:f.fn.tabs.defaults.contextMenu.defaultEventsHandler.closetab},{id:p+"_close_others",text:f.fn.tabs.defaults.contextMenu.itemname.closeothers,onclick:f.fn.tabs.defaults.contextMenu.defaultEventsHandler.closeOthersTab},{id:p+"_close_rightside",text:f.fn.tabs.defaults.contextMenu.itemname.closerightside,onclick:f.fn.tabs.defaults.contextMenu.defaultEventsHandler.closeRightSideTabs},{id:p+"_close_all",text:f.fn.tabs.defaults.contextMenu.itemname.closeall,onclick:f.fn.tabs.defaults.contextMenu.defaultEventsHandler.closeAll}]}function a(q,p){var r=[];p++;f(q).children("div.tabs-header").find("ul li:nth-child("+p+")").each(function(){r.push(this)});return r.length>0?r[0]:null}function b(v,s,p,w){if(typeof p=="number"&&typeof s=="number"){var x=f.data(v,"tabs").tabs;if(p<0||p>x.length){return}if(s<0||s>x.length){return}if(w){var z=x[p];for(var r=p;r>s;r--){x.splice(r,1,x[r-1])}x[s]=z;var y=a(v,s);if(y){var u=a(v,p);f(y).before(u)}}else{var z=x[s];for(var q=s;q<=p;q++){x.splice(q,1,x[q+1])}x[p]=z;var y=a(v,p);if(y){var t=a(v,s);f(y).after(t)}}}}function e(s){var q=f(s).tabs("tabs");var t=[];for(var p=0;p<q.length;p++){var r=q[p];if(r.panel("options").closable==undefined||!r.panel("options").closable){t.push(r)}}return t}function d(w,s,p,x,u){var t=f("<iframe>").attr("height","100%").attr("width","100%").attr("marginheight","0").attr("marginwidth","0").attr("frameborder","0");setTimeout(function(){t.attr("src",p)},1);var r=f(w).tabs("getTab",s);r.panel("body").css({overflow:"hidden"}).empty().append(t);if(x){var u=u||f.fn.datagrid.defaults.loadMsg;var v=r.panel("body");v.css("position","relative");var y=f('<div class="datagrid-mask" style="display:block;"></div>').appendTo(v);var q=f('<div class="datagrid-mask-msg" style="display:block; left: 50%;"></div>').html(u).appendTo(v);setTimeout(function(){q.css("marginLeft",-q.outerWidth()/2)},5)}t.bind("load",function(){if(t[0].contentWindow){r.panel("body").children("div.datagrid-mask-msg").remove();r.panel("body").children("div.datagrid-mask").remove()}})}f.fn.tabs.defaults.contextMenu={};f.fn.tabs.defaults.contextMenu.itemname={};f.fn.tabs.defaults.contextMenu.itemname.reload="重新加载";f.fn.tabs.defaults.contextMenu.itemname.fixtab="固定标签页";f.fn.tabs.defaults.contextMenu.itemname.unfixtab="取消固定标签";f.fn.tabs.defaults.contextMenu.itemname.close="关闭标签页";f.fn.tabs.defaults.contextMenu.itemname.closeothers="关闭其他标签页";f.fn.tabs.defaults.contextMenu.itemname.closerightside="关闭右侧标签页";f.fn.tabs.defaults.contextMenu.itemname.closeall="关闭所有标签页";f.fn.tabs.defaults.contextMenu.defaultEventsHandler={reload:function(s,u,r,t){var p=f(t).tabs("getTab",r);var q=p.panel("options").useiframe;if(q){f("iframe",p.panel("body")).each(function(){this.contentWindow.location.reload()})}else{p.panel("refresh")}},fixtab:function(s,u,q,t){var r=f(t).tabs("getTab",q);f(t).tabs("update",{tab:r,options:{closable:false}});var p=f.fn.tabs.defaults.customAttr.fixedtabs.length;b(t,p,q,true)},unfixtab:function(s,u,p,t){var q=e(t).length-1;var r=f(t).tabs("getTab",p);f(t).tabs("update",{tab:r,options:{closable:true}});b(t,p,q)},closetab:function(r,t,q,s){var p=f(s).tabs("getTab",q).panel("options");if(p.closable){f(s).tabs("close",q)}},closeOthersTab:function(t,s,q,u){var r=f(u).tabs("tabs");var p=f.grep(r,function(w,v){return w.panel("options").closable&&v!=q});f.each(p,function(){f(u).tabs("close",this.panel("options").title)})},closeRightSideTabs:function(s,u,q,t){var r=f(t).tabs("tabs");var p=f.grep(r,function(w,v){return v>q&&w.panel("options").closable});f.each(p,function(){f(t).tabs("close",this.panel("options").title)})},closeAll:function(s,u,q,t){var r=f(t).tabs("tabs");var p=f.grep(r,function(w,v){return w.panel("options").closable});f.each(p,function(){f(t).tabs("close",this.panel("options").title)})}};function c(t,p,s,r){var q=f(t).tabs("options");var u=q[p];switch(p){case"onLoad":if(r){q[p]=s}else{q[p]=function(v){u.apply(this,arguments);s.apply(this,arguments)}}break;case"onContextMenu":if(r){q[p]=s}else{q[p]=function(w,x,v){u.apply(this,arguments);s.apply(this,arguments)}}break;default:if(r){q[p]=s}else{q[p]=function(w,v){u.apply(this,arguments);s.apply(this,arguments)}}break}}f.fn.tabs.defaults.customAttr={fixedtabs:[0],contextMenu:{isShow:false,isMerge:true,items:[]}};var l=f.extend({},f.fn.tabs.methods);f.extend(f.fn.tabs.methods,{followCustomHandle:function(p){return p.each(function(){})},add:function(q,p){return q.each(function(){var r=null;if(p.href||/^url:/.test(p.content)){r=p.href||p.content.substr(4,p.content.length);delete p.content;delete p.href}if(r){if(p.useiframe){l.add(q,p);d(this,p.title,r,p.showMask,p.loadMsg)}else{l.add(q,f.extend(p,{href:r}))}}else{l.add(q,p)}if(p.css){f(this).tabs("getTab",p.title).css(p.css)}})},addEventListener:function(q,p){return q.each(function(){var r=f.isArray(p)?p:[p];var s=this;f.each(r,function(t,u){c(s,u.name,u.handler||function(){},u.override)})})}});var h=f.fn.tabs;f.fn.tabs=function(p,q){if(typeof p!="string"){return this.each(function(){h.call(f(this),p,q);o(this)})}else{return h.call(this,p,q)}};f.fn.tabs.methods=h.methods;f.fn.tabs.defaults=h.defaults;f.fn.tabs.parseOptions=h.parseOptions})(jQuery);(function($){function init(target){var options=$(target).toolbar("options");var tb=$(target).addClass("dialog-toolbar panel-body").css({height:"28px","line-height":"24px",overflow:"hidden"});if($.trim(tb.html()).length>0){return}tb.append('<table cellspacing="0" cellpadding="0"><tr></tr></table>');if(options.buttonPosition=="right"){tb.find("table").css("float","right")}if(options.data){addItems(target,options.data)}else{options.loader.call(target,function(data){options.data=data;addItems(target,options.data)},function(){options.onLoadError.apply(target,arguments)})}}function add(target,item){var tr=$(target).find("tr");if(typeof item=="string"&&$.trim(item)=="-"){$('<td><div class="dialog-tool-separator"></div></td>').appendTo(tr)}else{if($.trim(item.text)=="-"){$('<td><div class="dialog-tool-separator"></div></td>').appendTo(tr)}else{var td=$("<td></td>").appendTo(tr);var button=$('<a href="javascript:void(0)"></a>').appendTo(td);button[0].onclick=eval(item.handler||function(){});button.linkbutton($.extend({},item,{plain:true}))}}}function addItems(target,items){if(!$.isArray(items)){return}for(var i=0;i<items.length;i++){add(target,items[i])}}$.fn.toolbar=function(options,param){if(typeof options=="string"){return $.fn.toolbar.methods[options](this,param)}options=options||{};return this.each(function(){var state=$.data(this,"toolbar");if(state){$.extend(state.options,options)}else{$.data(this,"toolbar",{options:$.extend({},$.fn.toolbar.defaults,$.parser.parseOptions(this),options)});init(this)}})};$.fn.toolbar.methods={options:function(jq){return $.data(jq[0],"toolbar").options},add:function(jq,items){return jq.each(function(){addItems(this,items)})}};$.fn.toolbar.defaults={data:null,url:undefined,buttonPosition:"left",loader:function(success,error){var options=$(this).toolbar("options");$.ajax({type:"POST",url:options.url,dataType:"json",success:function(data){success(data)},error:function(){error.apply(this,arguments)}})},onLoadError:function(){}}})(jQuery);(function(g){function m(w){return g(w).attr("id")+"_contextmenu"}function t(y,z){var x=m(y);var w=g("#"+x);if(w.length==0){w=g("<div>",{id:x}).menu();w.menu("appendItems",z)}return w}function s(x){var w={};g.each(x,function(){var z=this;if(z.onclick){var y=z.id||z.text;w[y]=z.onclick;delete z.onclick}if(z.submenu&&g.isArray(z.submenu)&&z.submenu.length>0){g.extend(w,s(z.submenu))}});return w}function d(x){var w=m(x);return[{id:w+"_moveup",text:"位置上移",iconCls:"icon-moveup",onclick:e.contextmenu.defaultEvents.moveup},{id:w+"_movedown",text:"位置下移",iconCls:"icon-movedown",onclick:e.contextmenu.defaultEvents.movedown}]}function u(A){var x=g.extend(true,{},g.fn.tree.defaults,g(A).tree("options"));var w=x.customAttr.contextMenu;if(!w.isShow){return}var B=d(A);if(w.isMerge&&g.isArray(w.items)&&w.items.length>0){B=g.merge(B,w.items)}if(!w.isMerge&&g.isArray(w.items)&&w.items.length>0){B=w.items}var z=s(B);var y=t(A,B);g(A).tree("addEventListener",{name:"onContextMenu",handler:function(D,C){D.preventDefault();g(A).tree("select",C.target);y.menu("addEventListener",{name:"onClick",override:true,handler:function(F){var E=F.id||F.text;if(z[E]){z[E].call(this,F,C,A)}}}).menu("show",{left:D.pageX,top:D.pageY})}})}function n(C,x){var B=x.id||x.text;var D=g(C).tree("getParent",x.target);var w=f(C,D.target,false);var E=-1;for(var y=0,A=w.length;y<A;y++){var z=w[y].id||w[y].text;if(B==z){E=y-1;break}}if(E>-1){return w[E]}return null}function a(C,x){var B=x.id||x.text;var E=g(C).tree("getParent",x.target);var w=f(C,E.target,false);var D=-1;for(var y=0,A=w.length;y<A;y++){var z=w[y].id||w[y].text;if(B==z){D=y+1;break}}if(D>-1&&D<w.length){return w[D]}return null}function f(z,y,w){if(w){return g(z).tree("getChildren",y)}else{var x=[];g(y).next().find(">li>div.tree-node").each(function(){x.push(g(z).tree("getNode",this))});return x}}function b(x){var w=g.extend(true,{},g.fn.tree.defaults,g(x).tree("options"));if(!w.customAttr.expandOnNodeClick&&!w.customAttr.expandOnDblClick){return}if(w.customAttr.expandOnNodeClick){g(x).tree("addEventListener",{name:"onClick",handler:function(y){g(x).tree("toggle",y.target)}});return}if(w.customAttr.expandOnDblClick){g(x).tree("addEventListener",{name:"onDblClick",handler:function(y){g(x).tree("toggle",y.target)}})}}function k(y,x){var z=1;var w=g(y).tree("getParent",x.target);if(!w){return 1}return z+k(y,w)}function q(B,C,A){var w=A?[A]:g(B).tree("getRoots");for(var z=0;z<w.length;z++){var y=f(B,w[z].target,false);for(var x=0;x<y.length;x++){g(B).tree("expandTo",y[x].target)}C--;if(C>0){for(var x=0;x<y.length;x++){q(B,C,y[x])}}}}function r(x){var w=g.extend(true,{},g.fn.tree.defaults,g(x).tree("options"));if(!w.customAttr.onlyNodeExpand){return}g(x).tree("addEventListener",{name:"onBeforeExpand",handler:function(B){var A=g(x).tree("getParent",B.target);if(A){var z=f(x,A.target,false);for(var y=0;y<z.length;y++){if(z[y].state=="open"){g(x).tree("collapseAll",z[y].target)}}}else{g(x).tree("collapseAll")}}})}function o(A,w,z,y){var x=g(A).tree("options");var B=x[w];switch(w){case"onBeforeLoad":if(y){x[w]=z}else{x[w]=function(C,D){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onLoadSuccess":if(y){x[w]=z}else{x[w]=function(C,D){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onLoadError":if(y){x[w]=z}else{x[w]=function(C){B.apply(this,C);z.apply(this,C)}}break;case"onBeforeCheck":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onCheck":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onContextMenu":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onDragEnter":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onDragOver":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onDragLeave":if(y){x[w]=z}else{x[w]=function(D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onBeforeDrop":if(y){x[w]=z}else{x[w]=function(E,D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;case"onDrop":if(y){x[w]=z}else{x[w]=function(E,D,C){B.apply(this,arguments);z.apply(this,arguments)}}break;default:if(y){x[w]=z}else{x[w]=function(C){B.apply(this,arguments);z.apply(this,arguments)}}break}}function p(y,w){if(!y.attributes){y.attributes={}}for(var x=0;x<w.length;x++){y.attributes[w[x]]=y[w[x]]}}function v(x){var w=x.idField||x.textField||x.iconField||x.childrenField||x.attributesField||x.attributes||false;return w?true:false}function h(G,B){if(!v(G)){return B}var w=G.idField||"id",y=G.textField||"text",x=G.iconField||"iconCls",D=G.parentField||"pid",z=G.attributes||[];var E=[],F=[];for(var A=0,C=B.length;A<C;A++){F[B[A][w]]=B[A]}for(var A=0,C=B.length;A<C;A++){if(F[B[A][D]]&&B[A][w]!=B[A][D]){if(!F[B[A][D]]["children"]){F[B[A][D]]["children"]=[]}B[A]["text"]=B[A][y];B[A][x]&&(B[A]["iconCls"]=B[A][x]);p(B[A],z);F[B[A][D]]["children"].push(B[A])}else{B[A]["text"]=B[A][y];B[A][x]&&(B[A]["iconCls"]=B[A][x]);p(B[A],z);E.push(B[A])}}return E}function c(F,C){if(!v(F)){return C}var w=F.idField||"id",z=F.textField||"text",y=F.iconField||"iconCls",E=F.childrenField||"children",D=F.attributesField||"attributes",A=F.attributes||[];var x=function(H){if(!H.id&&H[w]){H.id=H[w]}if(!H.text&&H[z]){H.text=H[z]}if(!H.iconCls&&H[y]){H.iconCls=H[y]}if(!H.children&&H[E]){H.children=H[E]}if(!H.attributes&&H[D]){H.attributes=H[D]}if(A&&g.isArray(A)){p(H,A)}if(H.children){for(var G=0;G<H.children.length;G++){x(H.children[G])}}};for(var B=0;B<C.length;B++){x(C[B])}return C}function j(x,w){g(w.target).removeClass("tree-node-selected")}g.fn.tree.contextmenu={defaultEvents:{moveup:function(A,z,B){var x=g.extend(true,{},g.fn.tree.defaults,g(B).tree("options"));var y=n(B,z);if(y){var w=g(B).tree("pop",z.target);g(B).tree("insert",{before:y.target,data:w});x.customAttr.onAfterMove.call(this,y,z)}},movedown:function(A,z,B){var y=g.extend(true,{},g.fn.tree.defaults,g(B).tree("options"));var x=a(B,z);if(x){var w=g(B).tree("pop",z.target);g(B).tree("insert",{after:x.target,data:w});y.customAttr.onAfterMove.call(this,x,z)}}}};g.fn.tree.defaults.customAttr={idField:null,textField:null,parentField:null,iconField:null,childrenField:null,attributesField:null,attributes:null,dataModel:null,expandOnNodeClick:false,expandOnDblClick:false,onlyNodeExpand:false,contextMenu:{isShow:false,isMerge:true,items:[]},onAfterMove:function(x,w){}};g.fn.tree.defaults.loadFilter=function(y,x){var w=g(this).tree("options").customAttr;if(w){if(w.dataModel=="simpleData"){return h(w,y)}else{return c(w,y)}}return y};g.fn.combotree.defaults.loadFilter=g.fn.tree.defaults.loadFilter;var l=g.extend({},g.fn.tree.methods);g.extend(g.fn.tree.methods,{followCustomHandle:function(w){return w.each(function(){})},getLevel:function(x,w){return k(x[0],w)},expandTo:function(x,w){return x.each(function(){if(g.type(w)=="number"){var y=w;q(this,y)}else{l.expandTo(x,w)}})},addEventListener:function(x,w){return x.each(function(){var y=g.isArray(w)?w:[w];var z=this;g.each(y,function(A,B){o(z,B.name,B.handler||function(){},B.override)})})},unselect:function(x,w){return x.each(function(){j(this,w)})}});var e=g.fn.tree;g.fn.tree=function(w,x){if(typeof w!="string"){return this.each(function(){e.call(g(this),w,x);u(this);b(this);r(this)})}else{return e.call(this,w,x)}};g.fn.tree.methods=e.methods;g.fn.tree.defaults=e.defaults;g.fn.tree.parseOptions=e.parseOptions;g.fn.tree.contextMenu=e.contextMenu})(jQuery);(function(d){function e(p,o){return d(p).attr("id")+"_"+o}function h(r,s,o){var q=e(r,o);var p=d("#"+q);if(p.length==0){p=d("<div>",{id:q}).menu();p.menu("appendItems",s)}return p}function m(p){var o={};d.each(p,function(){var r=this;if(r.onclick){var q=r.id||r.text;o[q]=r.onclick;delete r.onclick}if(r.submenu&&d.isArray(r.submenu)&&r.submenu.length>0){d.extend(o,m(r.submenu))}});return o}function k(p){var o=e(p,"rowContextMenu");return[{id:o+"_delete",text:"删除",iconCls:"icon-remove",onclick:g.headerContextMenu.defaultEvents.doRemove},"-",{id:o+"_reload",text:"刷新",iconCls:"icon-reload",onclick:g.headerContextMenu.defaultEvents.doReload}]}function n(s){var o=d.extend(true,{},d.fn.treegrid.defaults,d(s).treegrid("options"));var p=o.customAttr.contextMenu;if(!p.isShow){return}var t=k(s);if(p.isMerge&&d.isArray(p.items)&&p.items.length>0){d.merge(t,p.items)}if(!p.isMerge&&d.isArray(p.items)&&p.items.length>0){t=p.items}var r=m(t);var q=h(s,t,"rowContextMenu");d(s).treegrid("addEventListener",{name:"onContextMenu",handler:function(v,w){v.preventDefault();d(s).treegrid("select",w[o.idField]);var u=q.menu("options");u.onClickCallback=u.onClickCallback||u.onClick;q.menu("addEventListener",{name:"onClick",override:true,handler:function(y){var x=y.id||y.text;if(r[x]){r[x].call(this,y,w,s)}}}).menu("show",{left:v.pageX,top:v.pageY})}})}function f(u){var t=e(u,"headerContextMenu");var o=[{text:"显示/隐藏列",iconCls:"icon-columns",submenu:[{id:t+"_showAll",text:"全部显示",iconCls:"icon-columns",onclick:function(w,x,v){d.fn.datagrid.headerContextMenu.defaultEvents.doShowAll(v)}},{id:t+"_restore",text:"还原",iconCls:"icon-columns",onclick:function(w,x,v){d.fn.datagrid.headerContextMenu.defaultEvents.doRestore(v)}},"-"]}];var p=function(v){return v.substr(v.lastIndexOf("_")+1,v.length)};var r=[];var s=d(u).treegrid("getColumnFields");var q=d(u).treegrid("options").treeField;d.each(s,function(w,y){if(!y){return true}var x=y==q?true:false;var v=d(u).treegrid("getColumnOption",y);v._hidden=v.hidden;r.push({id:t+"_"+y,text:v.title,iconCls:v.hidden?"icon-unchecked":"icon-checked",disabled:x,onclick:function(A,z,D){var C=p(A.id);var B=d(D).treegrid("getColumnOption",C).hidden;if(!B){d.fn.datagrid.headerContextMenu.defaultEvents.doHideColumn(D,C,A)}else{d.fn.datagrid.headerContextMenu.defaultEvents.doShowColumn(D,C,A)}}})});d.merge(o[0].submenu,r);return o}function l(r){var p=d.extend(true,{},d.fn.treegrid.defaults,d(r).treegrid("options"));var t=p.customAttr.headerContextMenu;if(!t.isShow){return}var s=f(r);if(t.isMerge){d.merge(s,t.items)}if(!t.isMerge&&d.isArray(t.items)&&t.items.length>0){s=t.items}var q=m(s);var o=h(r,s,"headerContextMenu");d(r).treegrid("addEventListener",{name:"onHeaderContextMenu",handler:function(v,u){v.preventDefault();o.menu("addEventListener",{name:"onClick",override:true,handler:function(x){var w=x.id||x.text;if(q[w]){q[w].call(this,x,u,r)}}}).menu("show",{left:v.pageX,top:v.pageY})}})}function a(r){var q=d.extend(true,{},d.fn.treegrid.defaults,d(r).treegrid("options"));if(!q.customAttr.expandOnNodeClick&&!q.customAttr.expandOnDblClick){return}var o=q.treeField;var p=q.idField;if(q.customAttr.expandOnNodeClick){d(r).treegrid("addEventListener",{name:"onClickCell",handler:function(s,t){if(o==s){d(r).treegrid("toggle",t[p])}}});return}if(q.customAttr.expandOnDblClick){d(r).treegrid("addEventListener",{name:"onDblClickCell",handler:function(s,t){if(o==s){d(r).treegrid("toggle",t[p])}}})}}function b(t){var p=d.extend(true,{},d.fn.treegrid.defaults,d(t).treegrid("options"));if(!p.customAttr.rowediting){return}var u=function(v){return d(v).attr("id")+"_editor_buttons_panel"};var o=120;var r=function(z){var y=u(z);if(d("#"+y).length>0){return}var w=d(z).treegrid("getPanel");var x=d(">div.datagrid-view>div.datagrid-view2>div.datagrid-body",w);x.css("position","relative");var v=d("<div>",{id:y}).addClass("dialog-button").appendTo(x).css({position:"absolute",display:"block","border-bottom":"1px solid #ddd","border-left":"1px solid #ddd","border-right":"1px solid #ddd",left:parseInt(w.width()/2)-o,"z-index":2013,display:"none",padding:"4px 5px"});d('<a href="javascript:void(0)">确定</a>').css("margin-left","0px").linkbutton({iconCls:"icon-ok"}).click(function(){var A=p.idField;var B=d(z).treegrid("getEditingRow");if(!p.customAttr.onConfirmEdit.call(z,B)){return}d(z).treegrid("endEdit",B[A])}).appendTo(v);d('<a href="javascript:void(0)">取消</a>').css("margin-left","6px").linkbutton({iconCls:"icon-cancel"}).click(function(){var A=p.idField;var B=d(z).treegrid("getEditingRow");d(z).treegrid("cancelEdit",B[A])}).appendTo(v)};var s=function(B,D){var w=p.idField;var C=p.finder.getTr(B,D[w],"body",2);var y=C.position();var x="#"+u(B);var v=d(B).treegrid("getPanel");var z=d(">div.datagrid-view>div.datagrid-view2>div.datagrid-body",v);var A=function(){var E=C.height(),F=C.width();var H=y.top+z.scrollTop(),G=y.left;var I=11;if(F>z.width()){G=z.width()/2-o}else{G=F/2-o}if(y.top+(E*2+I)>z.height()){H=H-(E+I)}else{H=H+E}return{top:H,left:G}};d(x).css(A()).show()};var q=function(w){var v="#"+u(w);d(v).hide()};d(t).treegrid("addEventListener",[{name:"onLoadSuccess",handler:function(w,v){r(this)}},{name:"onBeforeEdit",handler:function(v){s(t,v)}},{name:"onAfterEdit",handler:function(w,v){q(t)}},{name:"onCancelEdit",handler:function(v){q(t)}}])}function j(q){var p=d.extend(true,{},d.fn.treegrid.defaults,d(q).treegrid("options"));if(!p.customAttr.tooltip.enable){return}var t=function(w,v){var u={position:p.customAttr.tooltip.position,trackMouse:true,onHide:function(){d(w).tooltip("destroy")},onShow:function(){if(d.isPlainObject(v)&&v.css){d(this).tooltip("tip").css(v.css)}}};d.extend(u,d.isPlainObject(v)?v:{content:v});d(w).tooltip(u).tooltip("show")};var s=function(z,v){var y=d(z).attr("node-id");var x=d(q).treegrid("find",y);var u=function(C){var B=[];var A=d.grep(d.merge(d(q).treegrid("getColumnFields",true),d(q).treegrid("getColumnFields")),function(E,D){return d.trim(E).length>0});d.each(A,function(){var D=this;var E=d(q).treegrid("getColumnOption",D).title;B.push(E+": "+C[D])});return B.join("<br>")};var w=v?v(y,x):u(x);d(z).mouseover(function(){t(this,w)})};var o=function(u,v){u.mouseover(function(){var z=d(this).parent().attr("node-id");var x=d(q).treegrid("find",z);var A=d(this).attr("field");var y=x[A];var w=v?v(y,A,z,x):y;t(this,w)})};var r=function(){if(p.customAttr.tooltip.target=="row"){p.finder.getTr(q,"","allbody").each(function(){if(d(this).hasClass("datagrid-row")){s(this,p.customAttr.tooltip.formatter)}})}else{if(p.customAttr.tooltip.fields&&d.isArray(p.customAttr.tooltip.fields)){var u=d(q).treegrid("getPanel");var v=d(">div.datagrid-view>div.datagrid-view2>div.datagrid-body",u);d.each(p.customAttr.tooltip.fields,function(){var w=this;o(d("td[field="+w+"]",v),p.customAttr.tooltip.formatter)})}}};d(q).treegrid("addEventListener",{name:"onLoadSuccess",handler:function(v,u){r()}})}function c(s,o,r,q){var p=d(s).treegrid("options");var t=p[o];switch(o){case"onClickRow":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onDblClickRow":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onClickCell":if(q){p[o]=r}else{p[o]=function(u,v){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onDblClickCell":if(q){p[o]=r}else{p[o]=function(u,v){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onBeforeLoad":if(q){p[o]=r}else{p[o]=function(v,u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onLoadSuccess":if(q){p[o]=r}else{p[o]=function(v,u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onLoadError":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,u);r.apply(this,u)}}break;case"onBeforeExpand":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onExpand":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onBeforeCollapse":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onCollapse":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onContextMenu":if(q){p[o]=r}else{p[o]=function(u,v){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onBeforeEdit":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onAfterEdit":if(q){p[o]=r}else{p[o]=function(v,u){t.apply(this,arguments);r.apply(this,arguments)}}break;case"onCancelEdit":if(q){p[o]=r}else{p[o]=function(u){t.apply(this,arguments);r.apply(this,arguments)}}break;default:d(s).datagrid("addEventListener",{name:o,override:q,handler:r});break}}d.fn.treegrid.headerContextMenu={};d.fn.treegrid.headerContextMenu.defaultEvents={doRemove:function(o,q,p){d.messager.confirm("疑问","您确定要删除已选中的行？",function(t){if(t){var s=d(p).treegrid("options").idField;var u=q[s];d(p).treegrid("remove",u)}})},doReload:function(o,q,p){d(p).treegrid("reload")}};d.fn.treegrid.defaults.customAttr={iconField:null,parentField:null,expandOnNodeClick:false,expandOnDblClick:false,headerContextMenu:{isShow:false,isMerge:true,items:[]},contextMenu:{isShow:false,isMerge:true,items:[]},rowediting:false,tooltip:{enable:false,target:"row",position:"bottom",fields:undefined,formatter:undefined},onConfirmEdit:function(o){return true}};d.fn.treegrid.defaults.loadFilter=function(q,s){var w=d(this).treegrid("options");var v=w.customAttr;if(v&&v.parentField){var o=w.idField,u=v.parentField,p=v.iconField||"icon";for(var r=0,t=q.rows.length;r<t;r++){if(q.rows[r][u]&&q.rows[r][u]!="0"&&q.rows[r][o]!=q.rows[r][u]){q.rows[r]["_parentId"]=q.rows[r][u]}else{delete q.rows[r][u]}q.rows[r]["iconCls"]=q.rows[r][p]}}return q};d.extend(d.fn.treegrid.methods,{followCustomHandle:function(o){return o.each(function(){})},getEditingRow:function(p){var o=p.treegrid("getEditingRows");return o.length?o[0]:null},getEditingRows:function(q){var o=q.treegrid("options");var p=[];o.finder.getTr(q[0],"","allbody").each(function(){if(d(this).hasClass("datagrid-row-editing")){var r=d(this).attr("node-id");p.push(q.treegrid("find",r))}});return p},addEventListener:function(p,o){return p.each(function(){var q=d.isArray(o)?o:[o];var r=this;d.each(q,function(s,t){c(r,t.name,t.handler||function(){},t.override)})})}});var g=d.fn.treegrid;d.fn.treegrid=function(o,p){if(typeof o!="string"){return this.each(function(){g.call(d(this),o,p);l(this);n(this);a(this);b(this);j(this)})}else{return g.call(this,o,p)}};d.fn.treegrid.methods=g.methods;d.fn.treegrid.defaults=g.defaults;d.fn.treegrid.parseOptions=g.parseOptions})(jQuery);(function(d){function e(h){var f=d(h);var g=d.data(h,"validatebox");f.unbind(".validatebox");if(g.options.novalidate){return}f.bind("focus.validatebox",function(){g.validating=true;g.value=undefined;(function(){if(g.validating){if(g.value!=f.val()){g.value=f.val();if(g.timer){clearTimeout(g.timer)}g.timer=setTimeout(function(){d(h).validatebox("validate")},g.options.delay)}else{a(h)}setTimeout(arguments.callee,200)}})()}).bind("blur.validatebox",function(){if(g.timer){clearTimeout(g.timer);g.timer=undefined}g.validating=false;c(h)}).bind("mouseenter.validatebox",function(){if(f.hasClass("validatebox-invalid")){b(h)}}).bind("mouseleave.validatebox",function(){if(!g.validating){c(h)}})}function b(h){var g=d.data(h,"validatebox");var f=g.options;d(h).tooltip(d.extend({},f.tipOptions,{content:g.message,position:f.tipPosition,deltaX:f.deltaX})).tooltip("show");g.tip=true}function a(g){var f=d.data(g,"validatebox");if(f&&f.tip){d(g).tooltip("reposition")}}function c(g){var f=d.data(g,"validatebox");f.tip=false;d(g).tooltip("hide")}d.extend(d.fn.validatebox.defaults.rules,{unequal:{validator:function(f,g){return f!=g},message:d.fn.validatebox.defaults.missingMessage},minLength:{validator:function(f,g){return f.length>=g[0]}},equals:{validator:function(f,g){if(/^#/.test(g)){return f==d(g).val()}else{return f==g}}},english:{validator:function(f){return/^[A-Za-z]+$/i.test(f)}},code:{validator:function(f){return/^[A-Za-z0-9_\-]+$/i.test(f)}}});if(d.fn.validatebox){d.fn.validatebox.defaults.rules.minLength.message="请至少输入{0}个字符。";d.fn.validatebox.defaults.rules.equals.message="字段不匹配";d.fn.validatebox.defaults.rules.english.message="请输入英文字母（大小写不限）";d.fn.validatebox.defaults.rules.code.message="请输入英文字母（大小写不限）、数字、_或-"}})(jQuery);(function($){function getTop(w,options){var _doc;try{_doc=w.top.document;_doc.getElementsByTagName}catch(e){return w}if(options.locate=="document"||_doc.getElementsByTagName("frameset").length>0){return w}if(options.locate=="document.parent"||_doc.getElementsByTagName("frameset").length>0){return w.parent}return w.top}function setWindowSize(w,options){var _top=getTop(w,options);var wHeight=$(_top).height(),wWidth=$(_top).width();if(!/^#/.test(options.locate)){if(options.height=="auto"){options.height=wHeight*0.6}if(options.width=="auto"){options.width=wWidth*0.6}}else{var locate=/^#/.test(options.locate)?options.locate:"#"+options.locate;if(options.height=="auto"){options.height=$(locate).height()*0.6}if(options.width=="auto"){options.width=$(locate).width()*0.6}}}$.extend({showWindow:function(options){options=options||{};var target;var winOpts=$.extend({},{iconCls:"icon-form",useiframe:false,locate:"top",data:undefined,width:"60%",height:"60%",cache:false,minimizable:true,maximizable:true,collapsible:true,resizable:true,loadMsg:$.fn.datagrid.defaults.loadMsg,showMask:false,onClose:function(){target.dialog("destroy")}},options);var iframe=null;if(/^url:/.test(winOpts.content)){var url=winOpts.content.substr(4,winOpts.content.length);if(winOpts.useiframe){iframe=$("<iframe>").attr("height","100%").attr("width","100%").attr("marginheight",0).attr("marginwidth",0).attr("frameborder",0);setTimeout(function(){iframe.attr("src",url)},10)}else{winOpts.href=url}delete winOpts.content}var selfRefrence={getData:function(name){return winOpts.data?winOpts.data[name]:null},close:function(){target.panel("close")}};var _top=getTop(window,winOpts);var warpHandler=function(handler){if(typeof handler=="function"){return function(){handler(selfRefrence)}}if(typeof handler=="string"&&winOpts.useiframe){return function(){iframe[0].contentWindow[handler](selfRefrence)}}if(typeof handler=="string"){return function(){eval(_top[handler])(selfRefrence)}}};if(winOpts.toolbar&&$.isArray(winOpts.toolbar)){$.each(winOpts.toolbar,function(i,button){button.handler=warpHandler(button.handler)})}if(winOpts.buttons&&$.isArray(winOpts.buttons)){$.each(winOpts.buttons,function(i,button){button.handler=warpHandler(button.handler)})}var onLoadCallback=winOpts.onLoad;winOpts.onLoad=function(){onLoadCallback&&onLoadCallback.call(this,selfRefrence,_top)};if(!/^#/.test(winOpts.locate)){if(winOpts.useiframe&&iframe){if(winOpts.showMask){winOpts.onBeforeOpen=function(){var panel=$(this).panel("panel");var header=$(this).panel("header");var body=$(this).panel("body");body.css("position","relative");var mask=$('<div class="datagrid-mask" style="display:block;"></div>').appendTo(body);var msg=$('<div class="datagrid-mask-msg" style="display:block; left: 50%;"></div>').html(winOpts.loadMsg).appendTo(body);setTimeout(function(){msg.css("marginLeft",-msg.outerWidth()/2)},5)}}iframe.bind("load",function(){if(iframe[0].contentWindow){onLoadCallback&&onLoadCallback.call(this,selfRefrence,iframe[0].contentWindow);if(winOpts.showMask){target.panel("body").children("div.datagrid-mask-msg").remove();target.panel("body").children("div.datagrid-mask").remove()}}});target=_top.$("<div>").css({overflow:"hidden"}).append(iframe).dialog(winOpts)}else{target=_top.$("<div>").dialog(winOpts)}}else{var locate=/^#/.test(winOpts.locate)?winOpts.locate:"#"+winOpts.locate;target=$("<div>").appendTo(locate).dialog($.extend({},winOpts,{inline:true}))}return target},showModalDialog:function(options){options=options||{};var opts=$.extend({},options,{modal:true,minimizable:false,maximizable:false,resizable:false,collapsible:false});return $.showWindow(opts)}})})(jQuery);